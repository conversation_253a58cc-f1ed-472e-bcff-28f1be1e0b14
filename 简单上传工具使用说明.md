# Hubstudio 简单文件上传工具使用说明

## 🎯 工具概述

这是一个专门为Hubstudio用户设计的简化工具，主要功能是：
- 通过API打开Hubstudio中的浏览器环境
- 不需要管理账号，只负责打开环境
- 支持手动和自动化文件上传操作

## 📁 文件说明

- **`simple_browser_opener.py`** - 核心浏览器环境打开工具
- **`file_upload_tool.py`** - 专用文件上传工具
- **`simple_config.json`** - 简化配置文件
- **`简单上传工具使用说明.md`** - 本使用说明

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装必要的Python库
pip install requests selenium
```

### 2. 配置API凭证

编辑 `simple_config.json` 文件：

```json
{
    "api": {
        "app_id": "您的APP_ID",
        "app_secret": "您的APP_SECRET",
        "group_code": "您的团队代码"
    }
}
```

### 3. 基础使用

#### 方式一：只打开浏览器环境（推荐）

```bash
python simple_browser_opener.py
```

这个脚本会：
1. 自动登录Hubstudio API
2. 获取您的浏览器环境列表
3. 打开第一个可用环境
4. 显示环境信息
5. 让您选择是否保持环境打开

#### 方式二：使用文件上传工具

```bash
python file_upload_tool.py
```

这个脚本会：
1. 打开浏览器环境
2. 提供自动化上传功能
3. 支持单文件、多文件、文件夹上传

## 💡 使用场景

### 场景1：手动上传文件

1. 运行 `simple_browser_opener.py`
2. 选择保持环境打开
3. 在打开的浏览器中手动访问上传网站
4. 手动选择和上传文件

### 场景2：自动化上传文件

```python
from file_upload_tool import FileUploadTool

# 创建上传工具
tool = FileUploadTool()

# 初始化并打开浏览器
tool.initialize()
tool.open_browser_for_upload()

# 上传单个文件
tool.upload_single_file(
    file_path="C:/path/to/file.jpg",
    website_url="https://example.com/upload"
)

# 清理资源
tool.cleanup()
```

### 场景3：批量上传文件

```python
# 批量上传多个文件
file_list = [
    "C:/uploads/image1.jpg",
    "C:/uploads/image2.png",
    "C:/uploads/document.pdf"
]

results = tool.upload_multiple_files(
    file_paths=file_list,
    website_url="https://example.com/upload"
)

print(f"成功: {len(results['success'])}, 失败: {len(results['failed'])}")
```

### 场景4：上传整个文件夹

```python
# 上传文件夹中的所有图片
results = tool.upload_folder(
    folder_path="C:/uploads/images",
    website_url="https://example.com/upload",
    file_extensions=['.jpg', '.png', '.gif']
)
```

## ⚙️ 配置选项

### 基础API配置
```json
{
    "api": {
        "base_url": "http://localhost:6873",
        "app_id": "您的APP_ID",
        "app_secret": "您的APP_SECRET",
        "group_code": "您的团队代码"
    }
}
```

### 浏览器设置
```json
{
    "browser_settings": {
        "headless": false,
        "startup_urls": ["https://www.baidu.com"],
        "wait_time_after_open": 5
    }
}
```

### 上传设置
```json
{
    "upload_settings": {
        "default_upload_selector": "input[type='file']",
        "wait_timeout": 10
    }
}
```

### 环境选择设置
```json
{
    "environment_selection": {
        "auto_select_first": true,
        "preferred_environment_names": ["上传专用环境"],
        "exclude_environment_names": ["重要账号环境"]
    }
}
```

## 🔧 API功能说明

### SimpleBrowserOpener 类

#### 主要方法：

- **`login(app_id, app_secret, group_code)`** - 登录API
- **`get_environment_list()`** - 获取环境列表
- **`open_browser_environment(container_code, startup_urls)`** - 打开环境
- **`connect_to_browser(container_code)`** - 连接WebDriver
- **`close_environment(container_code)`** - 关闭环境
- **`close_all_environments()`** - 关闭所有环境

#### 使用示例：

```python
from simple_browser_opener import SimpleBrowserOpener

opener = SimpleBrowserOpener()

# 登录
opener.login("app_id", "app_secret", "group_code")

# 获取环境
environments = opener.get_environment_list()

# 打开第一个环境
env_id = str(environments[0]["containerCode"])
browser_info = opener.open_browser_environment(env_id)

# 连接浏览器进行自动化操作（可选）
driver = opener.connect_to_browser(env_id)
if driver:
    driver.get("https://example.com")
    # 进行其他操作...
    driver.quit()

# 关闭环境
opener.close_environment(env_id)
```

## 📋 常见问题

### Q1: 如何获取API凭证？
A: 请联系Hubstudio客服获取 app_id、app_secret 和 group_code

### Q2: 环境打开失败怎么办？
A: 检查以下几点：
- Hubstudio客户端是否正在运行
- API凭证是否正确
- 环境是否已被其他程序占用
- 系统资源是否充足

### Q3: 如何指定特定的环境？
A: 修改配置文件中的 `environment_selection` 部分，或直接传入环境ID

### Q4: 支持哪些文件类型上传？
A: 支持所有类型的文件，默认支持常见的图片、文档格式

### Q5: 如何自定义上传网站的选择器？
A: 在调用上传方法时传入 `upload_selector` 参数，或在配置文件中设置

## 🛠️ 高级用法

### 自定义上传逻辑

```python
def custom_upload_logic(driver, file_path, website_url):
    """自定义上传逻辑"""
    driver.get(website_url)
    
    # 等待页面加载
    time.sleep(2)
    
    # 查找上传按钮
    upload_btn = driver.find_element(By.ID, "upload-button")
    upload_btn.click()
    
    # 查找文件输入框
    file_input = driver.find_element(By.CSS_SELECTOR, "input[type='file']")
    file_input.send_keys(file_path)
    
    # 点击提交
    submit_btn = driver.find_element(By.ID, "submit")
    submit_btn.click()
    
    print("文件上传完成")

# 使用自定义逻辑
tool = FileUploadTool()
tool.initialize()
tool.open_browser_for_upload()

custom_upload_logic(
    tool.current_driver, 
    "C:/file.jpg", 
    "https://example.com"
)

tool.cleanup()
```

### 批量处理多个环境

```python
opener = SimpleBrowserOpener()
opener.login("app_id", "app_secret", "group_code")

environments = opener.get_environment_list()

# 为每个环境上传不同的文件
for i, env in enumerate(environments[:3]):
    env_id = str(env["containerCode"])
    
    # 打开环境
    opener.open_browser_environment(env_id)
    time.sleep(5)
    
    # 连接并上传
    driver = opener.connect_to_browser(env_id)
    if driver:
        file_path = f"C:/uploads/file_{i+1}.jpg"
        # 执行上传逻辑...
        driver.quit()
    
    # 关闭环境
    opener.close_environment(env_id)
```

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 检查Hubstudio官方文档
2. 联系Hubstudio技术支持：13305912638 / 18059169535
3. 查看程序输出的错误信息进行排查

## 🎉 总结

这个简化工具专门为需要通过Hubstudio进行文件上传的用户设计，具有：

- ✅ **简单易用** - 最少的配置，最直接的功能
- ✅ **灵活性** - 支持手动和自动化两种方式
- ✅ **可扩展** - 可以根据需要自定义上传逻辑
- ✅ **稳定性** - 完善的错误处理和资源管理

无论您是需要偶尔上传几个文件，还是需要批量处理大量文件，这个工具都能满足您的需求！
