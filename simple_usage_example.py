#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单使用示例
演示如何使用完整自动化流程
"""

from hubstudio_complete_automation import HubstudioCompleteAutomation

def example_1_basic_usage():
    """示例1：基础使用 - 只启动软件和打开环境"""
    print("📋 示例1：基础使用")
    print("-" * 30)
    
    # 创建自动化控制器
    automation = HubstudioCompleteAutomation()
    
    try:
        # 运行完整流程（不包含文件上传）
        success = automation.run_complete_automation()
        
        if success:
            print("✅ 基础流程完成，浏览器环境已打开")
            input("按回车键继续...")
        else:
            print("❌ 基础流程失败")
            
    finally:
        automation.cleanup()

def example_2_with_file_upload():
    """示例2：包含文件上传的完整流程"""
    print("📋 示例2：包含文件上传")
    print("-" * 30)
    
    # 配置文件上传参数
    file_path = r"C:\path\to\your\file.jpg"  # 请替换为实际文件路径
    upload_url = "https://example.com/upload"  # 请替换为实际上传网站
    
    automation = HubstudioCompleteAutomation()
    
    try:
        # 运行包含文件上传的完整流程
        success = automation.run_complete_automation(
            file_path=file_path,
            upload_url=upload_url
        )
        
        if success:
            print("✅ 完整流程（包含上传）完成")
            input("按回车键继续...")
        else:
            print("❌ 完整流程失败")
            
    finally:
        automation.cleanup()

def example_3_step_by_step():
    """示例3：分步骤执行"""
    print("📋 示例3：分步骤执行")
    print("-" * 30)
    
    automation = HubstudioCompleteAutomation()
    
    try:
        # 第一步：启动Hubstudio软件
        print("🚀 步骤1：启动Hubstudio软件...")
        if not automation.start_hubstudio_software():
            print("❌ 软件启动失败")
            return
        
        # 第二步：登录API
        print("🔑 步骤2：登录API...")
        if not automation.login_api():
            print("❌ API登录失败")
            return
        
        # 第三步：获取环境列表
        print("📋 步骤3：获取环境列表...")
        environments = automation.get_browser_environments()
        if not environments:
            print("❌ 没有可用环境")
            return
        
        # 第四步：选择环境
        print("🎯 步骤4：选择环境...")
        print("可用环境:")
        for i, env in enumerate(environments[:5]):
            print(f"  {i+1}. {env.get('containerName')} (ID: {env.get('containerCode')})")
        
        # 这里可以让用户选择，或者自动选择第一个
        selected_env = environments[0]
        container_code = str(selected_env["containerCode"])
        print(f"选择环境: {selected_env.get('containerName')} ({container_code})")
        
        # 第五步：打开环境
        print("🌐 步骤5：打开浏览器环境...")
        if not automation.open_browser_environment(container_code):
            print("❌ 环境打开失败")
            return
        
        print("✅ 分步骤执行完成！")
        print("浏览器环境已打开，您可以进行手动操作")
        
        input("按回车键关闭环境...")
        
    finally:
        automation.cleanup()

def example_4_multiple_environments():
    """示例4：打开多个环境"""
    print("📋 示例4：打开多个环境")
    print("-" * 30)
    
    automation = HubstudioCompleteAutomation()
    
    try:
        # 启动软件和登录
        if not automation.start_hubstudio_software():
            return
        if not automation.login_api():
            return
        
        # 获取环境列表
        environments = automation.get_browser_environments()
        if len(environments) < 2:
            print("❌ 需要至少2个环境来演示多环境打开")
            return
        
        # 打开前3个环境（或所有环境，如果少于3个）
        max_envs = min(3, len(environments))
        print(f"🌐 准备打开 {max_envs} 个环境...")
        
        for i in range(max_envs):
            env = environments[i]
            container_code = str(env["containerCode"])
            env_name = env.get('containerName', f'环境{i+1}')
            
            print(f"\n打开环境 {i+1}/{max_envs}: {env_name}")
            
            if automation.open_browser_environment(container_code):
                print(f"✅ 环境 {env_name} 打开成功")
            else:
                print(f"❌ 环境 {env_name} 打开失败")
            
            # 环境间延迟
            if i < max_envs - 1:
                print("⏳ 等待3秒后打开下一个环境...")
                import time
                time.sleep(3)
        
        print(f"\n✅ 多环境打开完成！已打开 {len(automation.opened_environments)} 个环境")
        input("按回车键关闭所有环境...")
        
    finally:
        automation.cleanup()

def main():
    """主菜单"""
    while True:
        print("\n" + "="*50)
        print("🎯 Hubstudio 自动化使用示例")
        print("="*50)
        print("请选择要运行的示例:")
        print("1. 基础使用（启动软件 + 打开环境）")
        print("2. 完整流程（包含文件上传）")
        print("3. 分步骤执行")
        print("4. 打开多个环境")
        print("0. 退出")
        print("="*50)
        
        try:
            choice = input("请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                example_1_basic_usage()
            elif choice == "2":
                example_2_with_file_upload()
            elif choice == "3":
                example_3_step_by_step()
            elif choice == "4":
                example_4_multiple_environments()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 执行异常: {e}")

if __name__ == "__main__":
    main()
