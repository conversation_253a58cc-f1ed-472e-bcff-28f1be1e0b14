# 技术实施方案详细规划

## 🛠️ 技术栈升级

### 后端技术栈
```yaml
核心框架:
  - Python 3.9+
  - FastAPI (替代Flask，更好的性能和类型支持)
  - Pydantic (数据验证)
  - SQLAlchemy 2.0 (ORM)

数据存储:
  - PostgreSQL (主数据库)
  - Redis (缓存和会话)
  - MinIO (对象存储，存储视频文件)
  - InfluxDB (时序数据，用于监控)

异步处理:
  - Celery (任务队列)
  - RabbitMQ (消息队列)
  - APScheduler (定时任务)

自动化工具:
  - Selenium Grid (浏览器自动化集群)
  - Appium (移动端自动化)
  - OpenCV (图像识别)
  - Tesseract (OCR)
```

### 前端技术栈
```yaml
框架和库:
  - React 18 (前端框架)
  - TypeScript (类型安全)
  - Ant Design (UI组件库)
  - React Query (数据获取)
  - Zustand (状态管理)

构建工具:
  - Vite (构建工具)
  - ESLint + Prettier (代码规范)
  - <PERSON><PERSON> (Git钩子)

可视化:
  - ECharts (数据可视化)
  - React Flow (流程图)
```

## 🏗️ 系统架构设计

### 微服务架构
```python
# 服务拆分策略
services = {
    "user_service": "用户认证和权限管理",
    "browser_service": "浏览器环境管理",
    "cloudphone_service": "云手机管理",
    "automation_service": "自动化任务执行",
    "content_service": "内容管理和分发",
    "analytics_service": "数据分析和报告",
    "notification_service": "通知和告警",
    "scheduler_service": "任务调度"
}
```

### 数据库设计
```sql
-- 核心表结构设计

-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    subscription_plan VARCHAR(20) DEFAULT 'basic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 浏览器环境表
CREATE TABLE browser_environments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    fingerprint_config JSONB,
    proxy_config JSONB,
    status VARCHAR(20) DEFAULT 'inactive',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 账号表
CREATE TABLE social_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    environment_id INTEGER REFERENCES browser_environments(id),
    platform VARCHAR(20) NOT NULL, -- youtube, tiktok, instagram等
    username VARCHAR(100),
    account_data JSONB, -- 存储账号相关数据
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容表
CREATE TABLE content_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    title VARCHAR(255),
    description TEXT,
    file_path VARCHAR(500),
    file_type VARCHAR(20),
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE automation_tasks (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    task_type VARCHAR(50), -- upload_video, nurture_account等
    config JSONB,
    status VARCHAR(20) DEFAULT 'pending',
    scheduled_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 核心模块实现

### 1. 浏览器环境管理模块
```python
# services/browser_service.py
from typing import List, Dict, Optional
import asyncio
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

class BrowserEnvironmentService:
    """浏览器环境管理服务"""
    
    def __init__(self):
        self.active_environments = {}
    
    async def create_environment(self, config: Dict) -> str:
        """创建浏览器环境"""
        options = Options()
        
        # 设置指纹参数
        if 'user_agent' in config:
            options.add_argument(f"--user-agent={config['user_agent']}")
        
        # 设置代理
        if 'proxy' in config:
            options.add_argument(f"--proxy-server={config['proxy']}")
        
        # 设置窗口大小
        if 'window_size' in config:
            options.add_argument(f"--window-size={config['window_size']}")
        
        # 禁用图片加载（提高速度）
        if config.get('disable_images', False):
            prefs = {"profile.managed_default_content_settings.images": 2}
            options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=options)
        env_id = f"env_{len(self.active_environments)}"
        self.active_environments[env_id] = driver
        
        return env_id
    
    async def batch_create_environments(self, configs: List[Dict]) -> List[str]:
        """批量创建环境"""
        tasks = [self.create_environment(config) for config in configs]
        return await asyncio.gather(*tasks)
    
    def get_environment(self, env_id: str) -> Optional[webdriver.Chrome]:
        """获取环境实例"""
        return self.active_environments.get(env_id)
    
    def close_environment(self, env_id: str):
        """关闭环境"""
        if env_id in self.active_environments:
            self.active_environments[env_id].quit()
            del self.active_environments[env_id]
```

### 2. TikTok自动化模块
```python
# services/tiktok_service.py
import asyncio
import random
from typing import List, Dict
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class TikTokAutomationService:
    """TikTok自动化服务"""
    
    def __init__(self, browser_service):
        self.browser_service = browser_service
    
    async def upload_video(self, env_id: str, video_path: str, metadata: Dict) -> bool:
        """上传视频到TikTok"""
        driver = self.browser_service.get_environment(env_id)
        if not driver:
            return False
        
        try:
            # 导航到TikTok上传页面
            driver.get("https://www.tiktok.com/upload")
            
            # 等待页面加载
            wait = WebDriverWait(driver, 10)
            
            # 上传视频文件
            file_input = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            file_input.send_keys(video_path)
            
            # 等待视频处理完成
            await asyncio.sleep(5)
            
            # 填写视频信息
            if 'caption' in metadata:
                caption_input = wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-text='true']"))
                )
                caption_input.clear()
                caption_input.send_keys(metadata['caption'])
            
            # 设置隐私设置
            if metadata.get('privacy') == 'public':
                public_radio = driver.find_element(By.CSS_SELECTOR, "[value='0']")
                public_radio.click()
            
            # 发布视频
            publish_button = wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-e2e='publish-button']"))
            )
            publish_button.click()
            
            # 等待发布完成
            await asyncio.sleep(10)
            
            return True
            
        except Exception as e:
            print(f"TikTok上传失败: {e}")
            return False
    
    async def nurture_account(self, env_id: str, actions: List[str]) -> bool:
        """智能养号"""
        driver = self.browser_service.get_environment(env_id)
        if not driver:
            return False
        
        try:
            driver.get("https://www.tiktok.com/foryou")
            
            for action in actions:
                if action == 'scroll':
                    # 模拟滚动
                    driver.execute_script("window.scrollBy(0, 500)")
                    await asyncio.sleep(random.uniform(2, 5))
                
                elif action == 'like':
                    # 随机点赞
                    like_buttons = driver.find_elements(By.CSS_SELECTOR, "[data-e2e='like-icon']")
                    if like_buttons:
                        random.choice(like_buttons).click()
                        await asyncio.sleep(random.uniform(1, 3))
                
                elif action == 'follow':
                    # 随机关注
                    follow_buttons = driver.find_elements(By.CSS_SELECTOR, "[data-e2e='follow-button']")
                    if follow_buttons:
                        random.choice(follow_buttons).click()
                        await asyncio.sleep(random.uniform(2, 4))
            
            return True
            
        except Exception as e:
            print(f"养号操作失败: {e}")
            return False
```

### 3. 任务调度模块
```python
# services/scheduler_service.py
from celery import Celery
from datetime import datetime, timedelta
import asyncio

app = Celery('hubstudio_automation')

class TaskSchedulerService:
    """任务调度服务"""
    
    def __init__(self):
        self.app = app
    
    def schedule_video_upload(self, user_id: int, video_data: Dict, schedule_time: datetime):
        """调度视频上传任务"""
        eta = schedule_time
        upload_video_task.apply_async(
            args=[user_id, video_data],
            eta=eta
        )
    
    def schedule_batch_upload(self, user_id: int, videos: List[Dict], interval_minutes: int = 30):
        """调度批量上传任务"""
        base_time = datetime.now()
        
        for i, video in enumerate(videos):
            schedule_time = base_time + timedelta(minutes=i * interval_minutes)
            self.schedule_video_upload(user_id, video, schedule_time)
    
    def schedule_account_nurturing(self, user_id: int, account_ids: List[str]):
        """调度账号养号任务"""
        for account_id in account_ids:
            # 每天执行2-3次养号操作
            for hour in [9, 15, 21]:
                nurture_account_task.apply_async(
                    args=[user_id, account_id],
                    eta=datetime.now().replace(hour=hour, minute=0, second=0)
                )

@app.task
def upload_video_task(user_id: int, video_data: Dict):
    """异步视频上传任务"""
    # 实现视频上传逻辑
    pass

@app.task
def nurture_account_task(user_id: int, account_id: str):
    """异步账号养号任务"""
    # 实现账号养号逻辑
    pass
```

## 🚀 部署方案

### Docker容器化
```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Chrome
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable

# 安装ChromeDriver
RUN CHROMEDRIVER_VERSION=`curl -sS chromedriver.storage.googleapis.com/LATEST_RELEASE` && \
    wget -N http://chromedriver.storage.googleapis.com/$CHROMEDRIVER_VERSION/chromedriver_linux64.zip && \
    unzip chromedriver_linux64.zip && \
    mv chromedriver /usr/local/bin/chromedriver && \
    chmod +x /usr/local/bin/chromedriver

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Kubernetes部署
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hubstudio-automation
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hubstudio-automation
  template:
    metadata:
      labels:
        app: hubstudio-automation
    spec:
      containers:
      - name: app
        image: hubstudio-automation:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 📊 监控和日志

### 监控指标
```python
# monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 业务指标
video_uploads_total = Counter('video_uploads_total', 'Total video uploads', ['platform', 'status'])
task_duration = Histogram('task_duration_seconds', 'Task duration', ['task_type'])
active_environments = Gauge('active_environments', 'Number of active browser environments')

# 系统指标
api_requests_total = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint'])
api_request_duration = Histogram('api_request_duration_seconds', 'API request duration')
```

### 日志配置
```python
# utils/logging.py
import logging
from pythonjsonlogger import jsonlogger

def setup_logging():
    logHandler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter()
    logHandler.setFormatter(formatter)
    logger = logging.getLogger()
    logger.addHandler(logHandler)
    logger.setLevel(logging.INFO)
    return logger
```

## 🔒 安全措施

### 数据加密
```python
# utils/encryption.py
from cryptography.fernet import Fernet
import os

class EncryptionService:
    def __init__(self):
        self.key = os.environ.get('ENCRYPTION_KEY', Fernet.generate_key())
        self.cipher = Fernet(self.key)
    
    def encrypt(self, data: str) -> str:
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        return self.cipher.decrypt(encrypted_data.encode()).decode()
```

### API安全
```python
# api/auth.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
```
