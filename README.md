# Hub Studio 全自动化视频上传系统

## 项目概述

本项目是一个基于图像识别的Hub Studio全自动化系统。从启动Hub Studio开始，通过图像识别技术自动完成以下完整流程：

1. **启动Hub Studio** → 2. **监控Hub Studio窗口** → 3. **自动分析环境列表** → 4. **打开浏览器环境** → 5. **跳转YouTube Studio** → 6. **执行视频上传**

整个流程完全基于图像识别，无需人工干预。

## 完整自动化流程

### 阶段1：Hub Studio启动与监控
- 自动启动Hub Studio应用程序
- 等待Hub Studio界面完全加载
- 识别并监控Hub Studio主窗口

### 阶段2：环境列表自动分析
- 通过图像识别定位环境列表区域
- 自动识别所有可用的浏览器环境
- 解析环境名称和状态信息

### 阶段3：浏览器环境自动打开
- 依次点击环境列表中的环境项
- 监控新弹出的浏览器窗口
- 验证浏览器环境成功启动

### 阶段4：YouTube Studio自动跳转
- 在浏览器中自动导航到YouTube Studio
- 识别并处理登录状态
- 确认进入YouTube Studio界面

### 阶段5：视频上传自动执行
- 通过图像识别定位上传按钮
- 自动选择视频文件
- 填写视频信息（标题、描述等）
- 完成视频发布流程

## 项目结构

```
hub_studio_automation/
├── src/
│   ├── main.py                    # 程序入口
│   ├── services/                  # 核心服务
│   │   ├── hub_studio_service.py  # Hub Studio启动和管理
│   │   ├── window_service.py      # 窗口监控和管理
│   │   ├── image_service.py       # 图像识别引擎
│   │   ├── environment_service.py # 环境分析和操作
│   │   ├── browser_service.py     # 浏览器控制
│   │   ├── youtube_service.py     # YouTube Studio操作
│   │   └── upload_service.py      # 视频上传处理
│   ├── utils/
│   │   └── logger.py              # 日志工具
│   └── config/
│       └── settings.py            # 配置文件
├── templates/                     # 图像识别模板
│   ├── hub_studio/               # Hub Studio相关模板
│   │   ├── hub_studio_icon.png
│   │   ├── environment_list.png
│   │   └── environment_item.png
│   ├── browser/                  # 浏览器相关模板
│   │   ├── address_bar.png
│   │   └── new_tab.png
│   ├── youtube/                  # YouTube Studio模板
│   │   ├── youtube_studio_logo.png
│   │   ├── create_button.png
│   │   ├── upload_video.png
│   │   ├── select_files.png
│   │   ├── title_field.png
│   │   ├── description_field.png
│   │   └── publish_button.png
│   └── common/                   # 通用模板
│       ├── close_button.png
│       └── confirm_button.png
├── videos/                       # 待上传视频目录
└── requirements.txt
```

## 核心代码实现

### 1. Hub Studio启动服务

```python
# services/hub_studio_service.py
import subprocess
import time
import psutil
from typing import Optional

class HubStudioService:
    """Hub Studio启动和管理服务"""

    def __init__(self):
        self.hub_studio_path = "C:\\Program Files\\HubStudio\\HubStudio.exe"
        self.process = None

    def start_hub_studio(self) -> bool:
        """启动Hub Studio"""
        try:
            # 检查是否已经运行
            if self.is_hub_studio_running():
                print("Hub Studio已在运行")
                return True

            print("正在启动Hub Studio...")
            self.process = subprocess.Popen([self.hub_studio_path])

            # 等待启动完成
            for i in range(30):  # 最多等待30秒
                if self.is_hub_studio_running():
                    print("Hub Studio启动成功")
                    return True
                time.sleep(1)

            print("Hub Studio启动超时")
            return False

        except Exception as e:
            print(f"启动Hub Studio失败: {e}")
            return False

    def is_hub_studio_running(self) -> bool:
        """检查Hub Studio是否在运行"""
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'hubstudio' in proc.info['name'].lower():
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False

    def wait_for_interface_ready(self) -> bool:
        """等待Hub Studio界面准备就绪"""
        from .image_service import ImageService

        image_service = ImageService()

        # 等待Hub Studio主界面出现
        for i in range(60):  # 最多等待60秒
            if image_service.find_element_by_template("templates/hub_studio/hub_studio_icon.png"):
                print("Hub Studio界面已准备就绪")
                return True
            time.sleep(1)

        print("等待Hub Studio界面超时")
        return False
```

### 2. 环境分析服务

```python
# services/environment_service.py
import time
from typing import List, Optional, Tuple
from .image_service import ImageService
from .window_service import WindowService

class EnvironmentService:
    """环境分析和操作服务"""

    def __init__(self):
        self.image_service = ImageService()
        self.window_service = WindowService()
        self.environments = []

    def analyze_environment_list(self) -> List[dict]:
        """自动分析Hub Studio中的环境列表"""
        print("开始分析环境列表...")

        # 确保Hub Studio窗口处于前台
        if not self.window_service.activate_hub_studio():
            print("无法激活Hub Studio窗口")
            return []

        # 定位环境列表区域
        list_area = self.locate_environment_list_area()
        if not list_area:
            print("无法定位环境列表区域")
            return []

        # 识别环境项目
        environments = self.identify_environment_items(list_area)
        self.environments = environments

        print(f"发现 {len(environments)} 个环境")
        return environments

    def locate_environment_list_area(self) -> Optional[Tuple[int, int, int, int]]:
        """定位环境列表区域"""
        # 方法1: 通过"我的环境"标题定位
        title_pos = self.image_service.find_text_by_ocr("我的环境")
        if title_pos:
            # 环境列表通常在标题下方
            x, y = title_pos
            return (x - 50, y + 30, 300, 400)  # (x, y, width, height)

        # 方法2: 通过环境列表模板定位
        list_pos = self.image_service.find_element_by_template("templates/hub_studio/environment_list.png")
        if list_pos:
            x, y = list_pos
            return (x - 100, y - 50, 300, 400)

        return None

    def identify_environment_items(self, list_area: Tuple[int, int, int, int]) -> List[dict]:
        """识别环境列表中的环境项目"""
        environments = []
        x, y, width, height = list_area

        # 在列表区域内搜索环境项目
        for i in range(10):  # 假设最多10个环境
            # 计算每个环境项的大概位置
            item_y = y + i * 60  # 假设每个环境项高度60像素

            if item_y > y + height:
                break

            # 在该位置查找环境项目模板
            item_pos = self.image_service.find_element_in_area(
                "templates/hub_studio/environment_item.png",
                (x, item_y, width, 60)
            )

            if item_pos:
                # 提取环境名称
                env_name = self.extract_environment_name(item_pos)

                environment = {
                    'id': f'env_{i}',
                    'name': env_name or f'环境_{i+1}',
                    'position': item_pos,
                    'status': 'available'
                }
                environments.append(environment)
                print(f"发现环境: {environment['name']} at {item_pos}")

        return environments

    def extract_environment_name(self, position: Tuple[int, int]) -> Optional[str]:
        """提取环境名称"""
        try:
            # 在环境项目位置附近进行OCR识别
            x, y = position
            name = self.image_service.find_text_in_area((x - 50, y - 10, 200, 30))
            return name
        except Exception:
            return None

    def open_environment(self, env_id: str) -> bool:
        """打开指定环境"""
        environment = next((env for env in self.environments if env['id'] == env_id), None)
        if not environment:
            print(f"未找到环境: {env_id}")
            return False

        print(f"正在打开环境: {environment['name']}")

        # 点击环境项目
        pos = environment['position']
        self.image_service.click_at_position(pos)

        # 等待浏览器窗口出现
        return self.window_service.wait_for_new_browser_window()
```

### 3. YouTube Studio服务

```python
# services/youtube_service.py
import time
from typing import Optional
from .image_service import ImageService
from .browser_service import BrowserService

class YouTubeService:
    """YouTube Studio操作服务"""

    def __init__(self):
        self.image_service = ImageService()
        self.browser_service = BrowserService()

    def navigate_to_youtube_studio(self) -> bool:
        """导航到YouTube Studio"""
        print("正在跳转到YouTube Studio...")

        # 确保浏览器窗口处于前台
        if not self.browser_service.activate_browser():
            return False

        # 在地址栏输入YouTube Studio URL
        if not self.browser_service.navigate_to_url("https://studio.youtube.com"):
            return False

        # 等待YouTube Studio页面加载
        return self.wait_for_youtube_studio_loaded()

    def wait_for_youtube_studio_loaded(self) -> bool:
        """等待YouTube Studio页面加载完成"""
        print("等待YouTube Studio页面加载...")

        for i in range(30):  # 最多等待30秒
            # 查找YouTube Studio标志性元素
            if self.image_service.find_element_by_template("templates/youtube/youtube_studio_logo.png"):
                print("YouTube Studio页面加载完成")
                return True

            # 检查是否需要登录
            if self.image_service.find_text_by_ocr("登录") or self.image_service.find_text_by_ocr("Sign in"):
                print("需要登录，请手动登录后继续...")
                # 这里可以添加自动登录逻辑
                time.sleep(10)  # 给用户时间手动登录

            time.sleep(1)

        print("YouTube Studio页面加载超时")
        return False

    def start_video_upload(self) -> bool:
        """开始视频上传流程"""
        print("开始视频上传...")

        # 查找并点击"创建"按钮
        create_btn = self.image_service.find_element_by_template("templates/youtube/create_button.png")
        if not create_btn:
            print("未找到创建按钮")
            return False

        self.image_service.click_at_position(create_btn)
        time.sleep(2)

        # 查找并点击"上传视频"选项
        upload_btn = self.image_service.find_element_by_template("templates/youtube/upload_video.png")
        if not upload_btn:
            print("未找到上传视频选项")
            return False

        self.image_service.click_at_position(upload_btn)
        time.sleep(2)

        return True

    def select_video_file(self, video_path: str) -> bool:
        """选择视频文件"""
        print(f"选择视频文件: {video_path}")

        # 查找文件选择按钮
        select_files_btn = self.image_service.find_element_by_template("templates/youtube/select_files.png")
        if not select_files_btn:
            print("未找到文件选择按钮")
            return False

        self.image_service.click_at_position(select_files_btn)
        time.sleep(2)

        # 在文件对话框中输入文件路径
        import pyautogui
        pyautogui.typewrite(video_path)
        pyautogui.press('enter')

        # 等待文件上传开始
        time.sleep(5)
        return True

    def fill_video_info(self, title: str, description: str = "") -> bool:
        """填写视频信息"""
        print("填写视频信息...")

        # 填写标题
        title_field = self.image_service.find_element_by_template("templates/youtube/title_field.png")
        if title_field:
            self.image_service.click_at_position(title_field)
            time.sleep(1)

            # 清空现有内容并输入新标题
            import pyautogui
            pyautogui.hotkey('ctrl', 'a')
            pyautogui.typewrite(title)
            print(f"已填写标题: {title}")

        # 填写描述（如果提供）
        if description:
            desc_field = self.image_service.find_element_by_template("templates/youtube/description_field.png")
            if desc_field:
                self.image_service.click_at_position(desc_field)
                time.sleep(1)
                pyautogui.typewrite(description)
                print(f"已填写描述: {description}")

        return True

    def publish_video(self) -> bool:
        """发布视频"""
        print("发布视频...")

        # 查找并点击发布按钮
        publish_btn = self.image_service.find_element_by_template("templates/youtube/publish_button.png")
        if not publish_btn:
            print("未找到发布按钮")
            return False

        self.image_service.click_at_position(publish_btn)

        # 等待发布完成
        print("等待视频发布完成...")
        time.sleep(10)

        return True
```

### 4. 主程序入口

```python
# main.py
import sys
import signal
import os
from services.hub_studio_service import HubStudioService
from services.environment_service import EnvironmentService
from services.youtube_service import YouTubeService

class AutomationController:
    """全自动化控制器"""

    def __init__(self):
        self.hub_studio_service = HubStudioService()
        self.environment_service = EnvironmentService()
        self.youtube_service = YouTubeService()
        self.is_running = False

    def run_full_automation(self, video_directory: str):
        """运行完整自动化流程"""
        print("=== Hub Studio 全自动化视频上传系统 ===")

        try:
            # 阶段1: 启动Hub Studio
            if not self.start_hub_studio():
                return False

            # 阶段2: 分析环境列表
            environments = self.analyze_environments()
            if not environments:
                return False

            # 阶段3: 获取视频文件列表
            video_files = self.get_video_files(video_directory)
            if not video_files:
                print("未找到视频文件")
                return False

            # 阶段4: 执行批量上传
            return self.execute_batch_upload(environments, video_files)

        except Exception as e:
            print(f"自动化流程出错: {e}")
            return False

    def start_hub_studio(self) -> bool:
        """启动Hub Studio"""
        print("\n[阶段1] 启动Hub Studio...")

        if not self.hub_studio_service.start_hub_studio():
            print("Hub Studio启动失败")
            return False

        if not self.hub_studio_service.wait_for_interface_ready():
            print("Hub Studio界面未准备就绪")
            return False

        print("✓ Hub Studio启动成功")
        return True

    def analyze_environments(self) -> list:
        """分析环境列表"""
        print("\n[阶段2] 分析环境列表...")

        environments = self.environment_service.analyze_environment_list()
        if environments:
            print(f"✓ 发现 {len(environments)} 个可用环境")
            for env in environments:
                print(f"  - {env['name']}")

        return environments

    def get_video_files(self, directory: str) -> list:
        """获取视频文件列表"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
        video_files = []

        for file in os.listdir(directory):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(os.path.join(directory, file))

        print(f"找到 {len(video_files)} 个视频文件")
        return video_files

    def execute_batch_upload(self, environments: list, video_files: list) -> bool:
        """执行批量上传"""
        print(f"\n[阶段3] 开始批量上传 {len(video_files)} 个视频...")

        success_count = 0

        for i, video_file in enumerate(video_files):
            env_index = i % len(environments)  # 循环使用环境
            environment = environments[env_index]

            print(f"\n--- 上传视频 {i+1}/{len(video_files)} ---")
            print(f"视频: {os.path.basename(video_file)}")
            print(f"环境: {environment['name']}")

            if self.upload_single_video(environment, video_file):
                success_count += 1
                print("✓ 上传成功")
            else:
                print("✗ 上传失败")

        print(f"\n=== 批量上传完成 ===")
        print(f"成功: {success_count}/{len(video_files)}")
        return success_count > 0

    def upload_single_video(self, environment: dict, video_file: str) -> bool:
        """上传单个视频"""
        try:
            # 1. 打开环境
            if not self.environment_service.open_environment(environment['id']):
                return False

            # 2. 跳转到YouTube Studio
            if not self.youtube_service.navigate_to_youtube_studio():
                return False

            # 3. 开始上传流程
            if not self.youtube_service.start_video_upload():
                return False

            # 4. 选择视频文件
            if not self.youtube_service.select_video_file(video_file):
                return False

            # 5. 填写视频信息
            video_title = os.path.splitext(os.path.basename(video_file))[0]
            if not self.youtube_service.fill_video_info(video_title):
                return False

            # 6. 发布视频
            if not self.youtube_service.publish_video():
                return False

            return True

        except Exception as e:
            print(f"上传视频时出错: {e}")
            return False

def signal_handler(signum, frame):
    print("\n收到停止信号，正在退出...")
    sys.exit(0)

def main():
    print("Hub Studio 全自动化视频上传系统")

    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法: python main.py <视频目录路径>")
        print("示例: python main.py C:\\Videos")
        sys.exit(1)

    video_directory = sys.argv[1]
    if not os.path.exists(video_directory):
        print(f"视频目录不存在: {video_directory}")
        sys.exit(1)

    # 创建自动化控制器
    controller = AutomationController()

    # 运行完整自动化流程
    success = controller.run_full_automation(video_directory)

    if success:
        print("自动化流程执行完成")
        sys.exit(0)
    else:
        print("自动化流程执行失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 使用方法

### 1. 环境准备
```bash
# 安装Python依赖
pip install opencv-python pyautogui pywin32 pytesseract psutil

# 安装Tesseract OCR (用于文字识别)
# 下载并安装: https://github.com/tesseract-ocr/tesseract
```

### 2. 准备模板图片
需要截取以下界面元素并保存到对应目录：

```
templates/
├── hub_studio/
│   ├── hub_studio_icon.png      # Hub Studio图标
│   ├── environment_list.png     # 环境列表区域
│   └── environment_item.png     # 环境项目模板
├── youtube/
│   ├── youtube_studio_logo.png  # YouTube Studio标志
│   ├── create_button.png        # 创建按钮
│   ├── upload_video.png         # 上传视频选项
│   ├── select_files.png         # 选择文件按钮
│   ├── title_field.png          # 标题输入框
│   ├── description_field.png    # 描述输入框
│   └── publish_button.png       # 发布按钮
└── browser/
    ├── address_bar.png          # 地址栏
    └── new_tab.png              # 新标签页
```

### 3. 配置设置
```python
# config/settings.py
class Config:
    # Hub Studio路径
    HUB_STUDIO_PATH = "C:\\Program Files\\HubStudio\\HubStudio.exe"

    # 图像识别阈值
    TEMPLATE_THRESHOLD = 0.8

    # 操作间隔时间（秒）
    CLICK_INTERVAL = 2
    WAIT_TIMEOUT = 30

    # 视频文件支持格式
    VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
```

### 4. 运行程序
```bash
# 运行完整自动化流程
python main.py C:\Videos

# 示例：上传D盘Videos文件夹中的所有视频
python main.py D:\Videos
```

### 5. 运行流程
程序将自动执行以下步骤：
1. 启动Hub Studio
2. 等待界面加载完成
3. 分析环境列表
4. 循环处理每个视频文件：
   - 打开浏览器环境
   - 跳转到YouTube Studio
   - 上传视频文件
   - 填写视频信息
   - 发布视频

## 重要注意事项

### 1. 系统要求
- Windows 10/11
- Python 3.8+
- 至少4GB内存
- 稳定的网络连接

### 2. Hub Studio要求
- Hub Studio已正确安装
- 至少配置一个可用的浏览器环境
- 环境已登录YouTube账号

### 3. 模板图片要求
- 模板图片必须与实际界面完全匹配
- 建议在相同分辨率下截取模板
- 模板图片应该是PNG格式

### 4. 使用建议
- 首次使用前在测试环境验证
- 确保视频文件格式符合YouTube要求
- 建议分批上传，避免触发平台限制
- 监控程序运行日志，及时处理异常

### 5. 故障排除
- 如果识别失败，检查模板图片是否匹配
- 如果Hub Studio启动失败，检查路径配置
- 如果上传失败，检查网络连接和账号状态
- 程序支持按Ctrl+C安全退出
