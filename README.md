# Hubstudio 浏览器环境管理工具

本项目提供了完整的Hubstudio指纹浏览器API使用文档和示例代码，帮助您通过编程方式管理浏览器环境。

## 📁 文件说明

### 核心文件
- **`Hubstudio浏览器环境管理文档.md`** - 详细的API文档和使用说明
- **`hubstudio_api_example.py`** - Python API使用示例
- **`config.json.example`** - 配置文件模板

## 🚀 快速开始

### 1. 环境准备

确保您已经：
- 安装了Hubstudio客户端（版本 ≥ V2.15.0.1）
- 启动了Hubstudio客户端
- 获得了API访问凭证（appId、appSecret、groupCode）

### 2. 安装依赖

```bash
pip install requests schedule
```

### 3. 配置API凭证

1. 复制配置文件模板：
```bash
cp config.json.example config.json
```

2. 编辑 `config.json`，填入您的API凭证：
```json
{
    "api": {
        "base_url": "http://localhost:6873",
        "app_id": "您的APP_ID",
        "app_secret": "您的APP_SECRET", 
        "group_code": "您的团队代码"
    }
}
```

### 4. 运行示例

```bash
python hubstudio_api_example.py
```

## 📖 主要功能

### 浏览器软件 vs 浏览器环境

- **浏览器软件**：Hubstudio客户端程序，提供API服务
- **浏览器环境**：在软件中创建的独立浏览器实例，每个都有独特的指纹配置

### 核心API功能

1. **环境管理**
   - 获取环境列表和详细信息
   - 创建、更新、删除环境
   - 批量操作环境

2. **环境控制**
   - 启动/关闭环境
   - 获取环境运行状态
   - 环境窗口管理

3. **监控和自动化**
   - 环境状态监控
   - 自动重启失败环境
   - 性能优化

## 🔧 API使用示例

### 基础操作

```python
from hubstudio_api_example import HubstudioAPI

# 创建API客户端
api = HubstudioAPI()

# 登录
api.login("app_id", "app_secret", "group_code")

# 获取环境列表
environments = api.get_environment_list()

# 启动环境
api.start_environment("环境ID", headless=False, urls=["https://www.baidu.com"])

# 关闭环境
api.stop_environment("环境ID")

# 获取统计信息
api.print_statistics()
```

### 批量操作

```python
# 批量启动环境
container_codes = ["123456", "789012", "345678"]
for code in container_codes:
    api.start_environment(code)
    time.sleep(2)  # 避免过快操作

# 获取所有环境状态
status_map = api.get_environment_status(container_codes)
for code, status in status_map.items():
    print(f"环境 {code}: {status}")
```

## 📊 环境状态说明

- **0** - 已开启：环境正在运行
- **1** - 开启中：环境正在启动
- **2** - 关闭中：环境正在关闭
- **3** - 已关闭：环境已停止

## ⚠️ 注意事项

1. **API限制**
   - 避免过于频繁的API调用
   - 建议在操作间添加适当延迟（1-2秒）

2. **资源管理**
   - 根据系统配置控制同时运行的环境数量
   - 推荐同时运行环境数不超过10个

3. **错误处理**
   - 实现完善的错误处理和重试机制
   - 记录操作日志便于问题排查

4. **安全性**
   - 妥善保管API凭证，避免泄露
   - 不要在代码中硬编码敏感信息

## 🛠️ 高级功能

### 环境监控

```python
from hubstudio_api_example import HubstudioAPI

class EnvironmentMonitor:
    def __init__(self, api):
        self.api = api
        self.target_environments = []
    
    def add_target(self, container_code):
        self.target_environments.append(container_code)
    
    def check_and_restart(self):
        status_map = self.api.get_environment_status(self.target_environments)
        for code, status in status_map.items():
            if status == 3:  # 已关闭
                print(f"重启环境: {code}")
                self.api.start_environment(code)

# 使用监控
api = HubstudioAPI()
api.login("app_id", "app_secret", "group_code")

monitor = EnvironmentMonitor(api)
monitor.add_target("123456")
monitor.check_and_restart()
```

### 性能优化

```python
def optimize_environments(api, max_concurrent=10):
    """控制并发环境数量"""
    environments = api.get_environment_list()
    container_codes = [str(env["containerCode"]) for env in environments]
    status_map = api.get_environment_status(container_codes)
    
    # 统计运行中的环境
    running = [code for code, status in status_map.items() if status in [0, 1]]
    
    # 如果超过限制，关闭多余的环境
    if len(running) > max_concurrent:
        excess = running[max_concurrent:]
        for code in excess:
            api.stop_environment(code)
```

## 📞 技术支持

- **官方网站**：https://www.hubstudio.cn/
- **客户端下载**：https://www.hubstudio.cn/download/
- **技术支持**：13305912638 / 18059169535
- **帮助文档**：https://support-orig.hubstudio.cn/

## 📝 更新日志

- **v1.0.0** - 初始版本，包含基础API功能
- 支持环境列表获取、启动、关闭等基本操作
- 提供统计信息和监控功能
- 包含完整的使用文档和示例代码

## 📄 许可证

本项目仅供学习和参考使用，请遵守Hubstudio的服务条款。
