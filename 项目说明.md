# Hubstudio 自动化控制系统

## 🎯 项目概述

这是一个完整的Hubstudio自动化控制系统，提供图形化界面和命令行两种使用方式，实现从软件启动到浏览器环境控制的全流程自动化。

### 核心功能
- ✅ **自动启动Hubstudio软件**
- ✅ **自动登录API服务**
- ✅ **获取和管理浏览器环境**
- ✅ **自动打开浏览器环境**
- ✅ **文件上传自动化**
- ✅ **实时日志监控**

## 📁 项目文件结构

```
📁 Hubstudio自动化系统
├── 🚀 main.py                          # 主启动文件
├── 🔧 启动GUI.bat                       # Windows启动脚本
├── 🖥️ hubstudio_gui.py                  # GUI主界面
├── 📋 gui_dialogs.py                    # GUI对话框模块
├── 🤖 hubstudio_complete_automation.py  # 自动化核心引擎
├── 📝 simple_usage_example.py           # 命令行使用示例
├── ⚙️ hubstudio_config.json             # 配置文件
├── 📖 GUI使用说明.md                    # GUI使用指南
├── 📖 正确的使用说明.md                  # 命令行使用指南
└── 📖 项目说明.md                       # 本文档
```

## 🚀 快速开始

### 方式一：一键启动（推荐）

1. **双击运行**
   ```
   双击 "启动GUI.bat" 文件
   ```

2. **选择运行模式**
   - 选择"是" → 图形界面模式（推荐新手）
   - 选择"否" → 命令行模式（适合高级用户）
   - 选择"取消" → 退出程序

### 方式二：Python命令启动

```bash
# 启动主程序（可选择模式）
python main.py

# 直接启动GUI
python hubstudio_gui.py

# 直接启动命令行示例
python simple_usage_example.py
```

## 🖥️ GUI界面模式

### 界面布局
```
┌─────────────────────────────────────────────────────────┐
│                 Hubstudio 自动化控制系统                  │
├─────────────────────────────────────────────────────────┤
│ 配置设置区域                                             │
│ ├─ Hubstudio路径: [自动检测] [浏览]                      │
│ ├─ APP ID: [输入框]                                     │
│ ├─ APP Secret: [密码框]                                 │
│ ├─ Group Code: [输入框]                                 │
│ └─ [保存配置]                                           │
├─────────────────────────────────────────────────────────┤
│ 控制操作区域                                             │
│ ├─ [🚀启动完整流程] [🛑停止] [🔄刷新环境] [🧹清理资源]      │
│ └─ 进度条: ████████████████████████████████████████     │
├─────────────────────────────────────────────────────────┤
│ 浏览器环境列表                                           │
│ ├─ 环境ID │ 环境名称 │ 状态 │ 代理类型 │ 最后使用IP        │
│ ├─ 123456 │ 测试环境 │ 运行 │ Socks5  │ ***********     │
│ └─ [打开选中环境] [关闭选中环境] [文件上传] [高级设置]      │
├─────────────────────────────────────────────────────────┤
│ 操作日志区域                                             │
│ ├─ [时间] INFO: 正在启动Hubstudio软件...                 │
│ ├─ [时间] INFO: API登录成功                             │
│ ├─ [时间] INFO: 获取到 5 个浏览器环境                    │
│ └─ [清空日志] [保存日志]                                 │
├─────────────────────────────────────────────────────────┤
│ 状态栏: 就绪                                            │
└─────────────────────────────────────────────────────────┘
```

### 主要功能

#### 1. 配置管理
- 自动检测Hubstudio安装路径
- 安全存储API凭证
- 一键保存配置

#### 2. 自动化控制
- 一键启动完整流程
- 实时进度显示
- 智能错误处理

#### 3. 环境管理
- 可视化环境列表
- 单击选择，双击打开
- 批量操作支持

#### 4. 文件上传
- 图形化上传配置
- 常用网站快速选择
- 高级选项设置

#### 5. 日志监控
- 实时操作日志
- 彩色状态显示
- 日志保存功能

## 💻 命令行模式

### 基础使用
```python
from hubstudio_complete_automation import HubstudioCompleteAutomation

# 创建自动化控制器
automation = HubstudioCompleteAutomation()

# 运行完整流程
automation.run_complete_automation()

# 清理资源
automation.cleanup()
```

### 分步骤控制
```python
# 第一步：启动Hubstudio软件
automation.start_hubstudio_software()

# 第二步：登录API
automation.login_api()

# 第三步：获取环境列表
environments = automation.get_browser_environments()

# 第四步：打开指定环境
container_code = str(environments[0]["containerCode"])
automation.open_browser_environment(container_code)

# 第五步：连接浏览器进行自动化
driver = automation.connect_to_browser(container_code)
# 进行自动化操作...
driver.quit()
```

## ⚙️ 配置说明

### hubstudio_config.json
```json
{
    "hubstudio": {
        "install_path": "",           // Hubstudio安装路径（空则自动检测）
        "startup_timeout": 60,        // 启动超时时间（秒）
        "api_port": 6873             // API服务端口
    },
    "api": {
        "app_id": "your_app_id",     // 您的APP ID
        "app_secret": "your_secret",  // 您的APP Secret
        "group_code": "your_group"    // 您的团队代码
    },
    "automation": {
        "wait_after_startup": 5,      // 环境启动后等待时间
        "default_startup_urls": [     // 默认启动URL
            "https://www.baidu.com"
        ]
    }
}
```

## 🔧 系统要求

### 软件环境
- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **Python**: 3.7 或更高版本
- **Hubstudio**: 已安装并可正常运行

### 硬件要求
- **内存**: 4GB+ （推荐8GB+）
- **CPU**: 双核心+ （推荐四核心+）
- **硬盘**: 2GB+ 可用空间
- **网络**: 稳定的互联网连接

### Python依赖库
```bash
pip install requests selenium psutil
```

## 🛠️ 安装步骤

### 1. 下载项目文件
将所有项目文件下载到同一个文件夹中。

### 2. 安装Python依赖
```bash
pip install requests selenium psutil
```

### 3. 配置API凭证
编辑 `hubstudio_config.json` 文件，填入您的API凭证。

### 4. 运行程序
双击 `启动GUI.bat` 或运行 `python main.py`

## 🎯 使用流程

### 完整自动化流程
```
开始
  ↓
启动程序 → 选择模式（GUI/命令行）
  ↓
配置API凭证
  ↓
点击"启动完整流程"
  ↓
系统自动执行：
├─ 启动Hubstudio软件
├─ 登录API服务
├─ 获取环境列表
├─ 打开浏览器环境
└─ 显示操作界面
  ↓
用户可以：
├─ 手动操作浏览器
├─ 使用文件上传功能
├─ 打开更多环境
└─ 查看操作日志
  ↓
完成操作 → 清理资源 → 结束
```

## 🔍 故障排除

### 常见问题

#### 1. 程序启动失败
- 检查Python环境是否正确安装
- 确认所有依赖库已安装
- 以管理员权限运行

#### 2. Hubstudio启动失败
- 检查安装路径是否正确
- 确认软件未被占用
- 检查系统资源

#### 3. API连接失败
- 验证API凭证是否正确
- 检查网络连接
- 确认软件已完全启动

#### 4. 环境打开失败
- 检查环境是否被占用
- 确认系统资源充足
- 尝试重启软件

## 📞 技术支持

### 获取帮助
1. 查看详细的使用说明文档
2. 检查操作日志了解错误信息
3. 联系Hubstudio官方技术支持

### 联系方式
- **官方网站**: https://www.hubstudio.cn/
- **技术支持**: 13305912638 / 18059169535
- **帮助文档**: https://support-orig.hubstudio.cn/

## 🎉 项目特色

### ✅ 用户友好
- 直观的图形化界面
- 一键启动完整流程
- 详细的操作指导

### ✅ 功能强大
- 完整的自动化流程
- 灵活的配置选项
- 强大的文件上传功能

### ✅ 稳定可靠
- 完善的错误处理
- 自动资源管理
- 详细的日志记录

### ✅ 易于使用
- 自动依赖检查
- 智能路径检测
- 傻瓜式操作

---

**让Hubstudio自动化变得简单高效！** 🚀
