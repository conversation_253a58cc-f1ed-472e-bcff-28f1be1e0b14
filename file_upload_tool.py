#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专用文件上传工具
通过Hubstudio浏览器环境进行文件上传操作
"""

import os
import json
import time
from pathlib import Path
from typing import List, Dict, Optional
from simple_browser_opener import SimpleBrowserOpener
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class FileUploadTool:
    """文件上传工具类"""
    
    def __init__(self, config_path: str = "simple_config.json"):
        self.config = self._load_config(config_path)
        self.opener = SimpleBrowserOpener()
        self.current_driver = None
        self.current_environment = None
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 配置文件 {config_path} 不存在")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return {}
    
    def initialize(self) -> bool:
        """初始化上传工具"""
        print("🔧 初始化文件上传工具...")
        
        # 获取API配置
        api_config = self.config.get("api", {})
        
        # 登录API
        return self.opener.login(
            api_config.get("app_id"),
            api_config.get("app_secret"),
            api_config.get("group_code")
        )
    
    def select_environment(self) -> Optional[str]:
        """选择合适的浏览器环境"""
        environments = self.opener.get_environment_list()
        if not environments:
            print("❌ 没有可用的浏览器环境")
            return None
        
        env_selection = self.config.get("environment_selection", {})
        
        # 如果配置了自动选择第一个
        if env_selection.get("auto_select_first", True):
            selected_env = environments[0]
            container_code = str(selected_env["containerCode"])
            print(f"🎯 自动选择环境: {selected_env['containerName']} ({container_code})")
            return container_code
        
        # 根据偏好名称选择
        preferred_names = env_selection.get("preferred_environment_names", [])
        exclude_names = env_selection.get("exclude_environment_names", [])
        
        for env in environments:
            env_name = env.get("containerName", "")
            
            # 跳过排除的环境
            if any(exclude in env_name for exclude in exclude_names):
                continue
            
            # 优先选择偏好的环境
            if any(prefer in env_name for prefer in preferred_names):
                container_code = str(env["containerCode"])
                print(f"🎯 选择偏好环境: {env_name} ({container_code})")
                return container_code
        
        # 如果没有找到偏好环境，选择第一个不在排除列表的
        for env in environments:
            env_name = env.get("containerName", "")
            if not any(exclude in env_name for exclude in exclude_names):
                container_code = str(env["containerCode"])
                print(f"🎯 选择环境: {env_name} ({container_code})")
                return container_code
        
        print("❌ 没有找到合适的环境")
        return None
    
    def open_browser_for_upload(self, container_code: str = None) -> bool:
        """打开浏览器环境准备上传"""
        if not container_code:
            container_code = self.select_environment()
            if not container_code:
                return False
        
        # 获取浏览器设置
        browser_settings = self.config.get("browser_settings", {})
        startup_urls = browser_settings.get("startup_urls", ["https://www.baidu.com"])
        
        # 打开浏览器环境
        browser_info = self.opener.open_browser_environment(
            container_code,
            startup_urls=startup_urls
        )
        
        if not browser_info:
            return False
        
        # 等待环境启动
        wait_time = browser_settings.get("wait_time_after_open", 5)
        print(f"⏳ 等待环境启动 {wait_time} 秒...")
        time.sleep(wait_time)
        
        # 连接到浏览器
        self.current_driver = self.opener.connect_to_browser(container_code)
        self.current_environment = container_code
        
        return self.current_driver is not None
    
    def upload_single_file(self, file_path: str, website_url: str, 
                          upload_selector: str = None) -> bool:
        """上传单个文件"""
        if not self.current_driver:
            print("❌ 浏览器未连接，请先调用 open_browser_for_upload()")
            return False
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        
        # 使用默认选择器或配置中的选择器
        if not upload_selector:
            upload_selector = self.config.get("upload_settings", {}).get(
                "default_upload_selector", "input[type='file']"
            )
        
        try:
            print(f"📁 上传文件: {os.path.basename(file_path)}")
            print(f"🌐 目标网站: {website_url}")
            
            # 访问网站
            self.current_driver.get(website_url)
            
            # 等待页面加载
            timeout = self.config.get("upload_settings", {}).get("wait_timeout", 10)
            wait = WebDriverWait(self.current_driver, timeout)
            
            # 查找文件上传元素
            upload_element = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, upload_selector))
            )
            
            # 上传文件
            upload_element.send_keys(os.path.abspath(file_path))
            print(f"✅ 文件 {os.path.basename(file_path)} 上传成功")
            
            return True
            
        except Exception as e:
            print(f"❌ 文件上传失败: {e}")
            return False
    
    def upload_multiple_files(self, file_paths: List[str], website_url: str,
                            upload_selector: str = None, delay_between_uploads: int = 2) -> Dict:
        """批量上传多个文件"""
        results = {"success": [], "failed": []}
        
        for i, file_path in enumerate(file_paths):
            print(f"\n📤 上传进度: {i+1}/{len(file_paths)}")
            
            if self.upload_single_file(file_path, website_url, upload_selector):
                results["success"].append(file_path)
            else:
                results["failed"].append(file_path)
            
            # 上传间隔
            if i < len(file_paths) - 1:
                print(f"⏳ 等待 {delay_between_uploads} 秒后继续...")
                time.sleep(delay_between_uploads)
        
        return results
    
    def upload_folder(self, folder_path: str, website_url: str,
                     file_extensions: List[str] = None, upload_selector: str = None) -> Dict:
        """上传文件夹中的所有文件"""
        if not os.path.exists(folder_path):
            print(f"❌ 文件夹不存在: {folder_path}")
            return {"success": [], "failed": []}
        
        # 默认支持的文件扩展名
        if not file_extensions:
            file_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt', '.doc', '.docx']
        
        # 收集文件
        file_paths = []
        for ext in file_extensions:
            pattern = f"*{ext}"
            file_paths.extend(Path(folder_path).glob(pattern))
        
        # 转换为字符串路径
        file_paths = [str(path) for path in file_paths]
        
        if not file_paths:
            print(f"❌ 文件夹中没有找到支持的文件类型: {file_extensions}")
            return {"success": [], "failed": []}
        
        print(f"📁 找到 {len(file_paths)} 个文件待上传")
        
        return self.upload_multiple_files(file_paths, website_url, upload_selector)
    
    def close_browser(self):
        """关闭浏览器连接"""
        if self.current_driver:
            try:
                self.current_driver.quit()
                print("✅ 浏览器连接已关闭")
            except:
                pass
            self.current_driver = None
    
    def close_environment(self):
        """关闭浏览器环境"""
        self.close_browser()
        
        if self.current_environment:
            self.opener.close_environment(self.current_environment)
            self.current_environment = None
    
    def cleanup(self):
        """清理所有资源"""
        self.close_browser()
        self.opener.close_all_environments()


def main():
    """主函数 - 演示文件上传工具的使用"""
    
    print("📤 Hubstudio 文件上传工具")
    print("=" * 40)
    
    # 创建上传工具
    upload_tool = FileUploadTool()
    
    try:
        # 1. 初始化
        if not upload_tool.initialize():
            print("❌ 初始化失败")
            return
        
        # 2. 打开浏览器环境
        if not upload_tool.open_browser_for_upload():
            print("❌ 浏览器环境打开失败")
            return
        
        print("✅ 浏览器环境已准备就绪")
        print("\n" + "=" * 40)
        print("现在您可以:")
        print("1. 手动在浏览器中访问上传网站")
        print("2. 使用自动化上传功能")
        print("3. 保持环境打开进行其他操作")
        
        # 示例：自动化上传（取消注释以使用）
        """
        # 上传单个文件示例
        file_path = r"C:\path\to\your\file.jpg"
        website_url = "https://example.com/upload"
        
        if os.path.exists(file_path):
            upload_tool.upload_single_file(file_path, website_url)
        
        # 批量上传示例
        file_list = [
            r"C:\path\to\file1.jpg",
            r"C:\path\to\file2.png"
        ]
        results = upload_tool.upload_multiple_files(file_list, website_url)
        print(f"上传结果: 成功 {len(results['success'])} 个，失败 {len(results['failed'])} 个")
        
        # 上传文件夹示例
        folder_path = r"C:\path\to\upload\folder"
        results = upload_tool.upload_folder(folder_path, website_url)
        """
        
        # 等待用户操作
        input("\n按回车键关闭环境...")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    finally:
        # 清理资源
        upload_tool.cleanup()
        print("🧹 资源清理完成")


if __name__ == "__main__":
    main()
