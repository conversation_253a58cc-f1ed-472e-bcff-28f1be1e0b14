# Hubstudio 浏览器环境管理文档

## 概述

Hubstudio 是一款专业的指纹浏览器软件，主要用于多账号管理、跨境电商、社媒营销等场景。本文档详细说明了浏览器软件与浏览器环境的区别，以及如何通过API获取和管理浏览器环境信息。

## 1. 浏览器软件 vs 浏览器环境

### 1.1 浏览器软件（Hubstudio Client）
- **定义**：Hubstudio客户端软件本身，是安装在本地计算机上的应用程序
- **功能**：提供图形界面、环境管理、API服务等核心功能
- **特点**：
  - 单一安装实例
  - 提供本地API服务（默认端口6873）
  - 支持多种浏览器内核（Chrome、Firefox）
  - 版本要求：V2.15.0.1以上支持API功能

### 1.2 浏览器环境（Browser Environment）
- **定义**：在Hubstudio软件中创建的独立浏览器实例，每个环境都有独特的指纹配置
- **功能**：模拟不同的设备、地理位置、网络环境等
- **特点**：
  - 每个环境有唯一的containerCode（环境ID）
  - 独立的指纹配置（UA、分辨率、时区、语言等）
  - 独立的代理设置
  - 独立的Cookie和本地存储
  - 可以同时运行多个环境

## 2. 环境状态管理

### 2.1 环境状态类型
- **0**：已开启
- **1**：开启中
- **2**：关闭中
- **3**：已关闭

### 2.2 环境生命周期
1. **创建环境** → 2. **配置指纹** → 3. **启动环境** → 4. **使用环境** → 5. **关闭环境**

## 3. API接口详解

### 3.1 基础配置
- **API基础URL**：`http://localhost:6873`
- **认证方式**：需要先登录获取访问权限
- **Content-Type**：`application/json`

### 3.2 账号登录
```http
POST /login
Content-Type: application/json

{
    "appId": "your_app_id",
    "appSecret": "your_app_secret", 
    "groupCode": "your_group_code"
}
```

### 3.3 获取环境列表
```http
POST /api/v1/env/list
Content-Type: application/json

{
    "current": 1,
    "size": 200
}
```

**响应示例**：
```json
{
    "msg": "Success",
    "code": 0,
    "data": {
        "list": [
            {
                "containerCode": 8256337,
                "containerName": "环境名称",
                "createTime": "2022-09-14 17:12:20",
                "lastUsedIp": "************",
                "proxyTypeName": "Socks5",
                "tagName": "分组名称",
                "ua": "Mozilla/5.0..."
            }
        ],
        "total": 2
    }
}
```

### 3.4 获取所有环境状态
```http
POST /api/v1/browser/all-browser-status
Content-Type: application/json

{
    "containerCodes": ["123094597"]
}
```

### 3.5 启动环境
```http
POST /api/v1/browser/start
Content-Type: application/json

{
    "containerCode": "223012801",
    "isWebDriverReadOnlyMode": false,
    "isHeadless": false,
    "containerTabs": ["https://www.example.com"]
}
```

**响应示例**：
```json
{
    "msg": "Success",
    "code": 0,
    "data": {
        "browserID": "25633",
        "debuggingPort": 46973,
        "webdriver": "C:\\Users\\<USER>\\chromedriver.exe",
        "statusCode": 0
    }
}
```

### 3.6 关闭环境
```http
POST /api/v1/browser/stop
Content-Type: application/json

{
    "containerCode": "223012801"
}
```

## 4. 环境管理最佳实践

### 4.1 获取环境数量信息
```python
import requests
import json

# API基础配置
BASE_URL = "http://localhost:6873"
headers = {"Content-Type": "application/json"}

def get_environment_count():
    """获取环境总数和状态统计"""
    
    # 1. 获取所有环境列表
    list_url = f"{BASE_URL}/api/v1/env/list"
    list_data = {"current": 1, "size": 200}
    
    response = requests.post(list_url, headers=headers, json=list_data)
    result = response.json()
    
    if result["code"] == 0:
        environments = result["data"]["list"]
        total_count = result["data"]["total"]
        
        # 提取所有环境ID
        container_codes = [str(env["containerCode"]) for env in environments]
        
        # 2. 获取环境状态
        status_url = f"{BASE_URL}/api/v1/browser/all-browser-status"
        status_data = {"containerCodes": container_codes}
        
        status_response = requests.post(status_url, headers=headers, json=status_data)
        status_result = status_response.json()
        
        # 3. 统计状态
        status_count = {"opened": 0, "opening": 0, "closing": 0, "closed": 0}
        
        if status_result["code"] == 0:
            for container in status_result["data"]["containers"]:
                status = container["status"]
                if status == 0:
                    status_count["opened"] += 1
                elif status == 1:
                    status_count["opening"] += 1
                elif status == 2:
                    status_count["closing"] += 1
                elif status == 3:
                    status_count["closed"] += 1
        
        return {
            "total_environments": total_count,
            "status_statistics": status_count,
            "environments": environments
        }
    
    return None

# 使用示例
if __name__ == "__main__":
    info = get_environment_count()
    if info:
        print(f"环境总数: {info['total_environments']}")
        print(f"已开启: {info['status_statistics']['opened']}")
        print(f"开启中: {info['status_statistics']['opening']}")
        print(f"关闭中: {info['status_statistics']['closing']}")
        print(f"已关闭: {info['status_statistics']['closed']}")
```

### 4.2 批量环境操作
```python
def batch_open_environments(container_codes, max_concurrent=5):
    """批量打开环境（限制并发数）"""
    import threading
    import time
    
    def open_single_environment(code):
        url = f"{BASE_URL}/api/v1/browser/start"
        data = {"containerCode": str(code)}
        
        try:
            response = requests.post(url, headers=headers, json=data)
            result = response.json()
            if result["code"] == 0:
                print(f"环境 {code} 启动成功")
                return True
            else:
                print(f"环境 {code} 启动失败: {result['msg']}")
                return False
        except Exception as e:
            print(f"环境 {code} 启动异常: {str(e)}")
            return False
    
    # 分批处理
    for i in range(0, len(container_codes), max_concurrent):
        batch = container_codes[i:i+max_concurrent]
        threads = []
        
        for code in batch:
            thread = threading.Thread(target=open_single_environment, args=(code,))
            threads.append(thread)
            thread.start()
        
        # 等待当前批次完成
        for thread in threads:
            thread.join()
        
        # 批次间延迟
        if i + max_concurrent < len(container_codes):
            time.sleep(2)

def close_all_environments():
    """关闭所有环境"""
    url = f"{BASE_URL}/api/v1/browser/stop-all"
    data = {"clearOpening": True}
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    if result["code"] == 0:
        print("所有环境已关闭")
        return True
    else:
        print(f"关闭失败: {result['msg']}")
        return False
```

## 5. 错误处理和故障排除

### 5.1 常见错误码
- **0**：成功
- **-10003**：登录失败
- **-10004**：未找到环境信息
- **-10008**：系统资源不足
- **-10013**：环境正在运行
- **-10017**：Firefox内核环境无法通过API打开

### 5.2 最佳实践建议
1. **资源管理**：避免同时打开过多环境，建议根据系统配置控制并发数
2. **错误重试**：对于网络错误或临时故障，实现重试机制
3. **状态监控**：定期检查环境状态，及时处理异常环境
4. **日志记录**：记录所有API调用和响应，便于问题排查

## 6. 高级功能示例

### 6.1 环境自动化管理类
```python
import requests
import json
import time
import threading
from typing import List, Dict, Optional

class HubstudioManager:
    def __init__(self, base_url="http://localhost:6873"):
        self.base_url = base_url
        self.headers = {"Content-Type": "application/json"}
        self.session = requests.Session()

    def login(self, app_id: str, app_secret: str, group_code: str) -> bool:
        """登录Hubstudio"""
        url = f"{self.base_url}/login"
        data = {
            "appId": app_id,
            "appSecret": app_secret,
            "groupCode": group_code
        }

        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            return result.get("code") == 0
        except Exception as e:
            print(f"登录失败: {e}")
            return False

    def get_all_environments(self) -> List[Dict]:
        """获取所有环境信息"""
        url = f"{self.base_url}/api/v1/env/list"
        data = {"current": 1, "size": 200}

        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            if result.get("code") == 0:
                return result["data"]["list"]
        except Exception as e:
            print(f"获取环境列表失败: {e}")
        return []

    def get_environment_status(self, container_codes: List[str]) -> Dict:
        """获取环境状态"""
        url = f"{self.base_url}/api/v1/browser/all-browser-status"
        data = {"containerCodes": container_codes}

        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            if result.get("code") == 0:
                return {container["containerCode"]: container["status"]
                       for container in result["data"]["containers"]}
        except Exception as e:
            print(f"获取环境状态失败: {e}")
        return {}

    def start_environment(self, container_code: str, headless: bool = False) -> Optional[Dict]:
        """启动单个环境"""
        url = f"{self.base_url}/api/v1/browser/start"
        data = {
            "containerCode": container_code,
            "isHeadless": headless,
            "isWebDriverReadOnlyMode": False
        }

        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            if result.get("code") == 0:
                return result["data"]
            else:
                print(f"启动环境 {container_code} 失败: {result.get('msg')}")
        except Exception as e:
            print(f"启动环境 {container_code} 异常: {e}")
        return None

    def stop_environment(self, container_code: str) -> bool:
        """关闭单个环境"""
        url = f"{self.base_url}/api/v1/browser/stop"
        data = {"containerCode": container_code}

        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            return result.get("code") == 0
        except Exception as e:
            print(f"关闭环境 {container_code} 异常: {e}")
        return False

    def get_environment_statistics(self) -> Dict:
        """获取环境统计信息"""
        environments = self.get_all_environments()
        if not environments:
            return {}

        container_codes = [str(env["containerCode"]) for env in environments]
        status_map = self.get_environment_status(container_codes)

        stats = {
            "total": len(environments),
            "opened": 0,
            "opening": 0,
            "closing": 0,
            "closed": 0,
            "by_group": {},
            "by_proxy_type": {}
        }

        for env in environments:
            code = str(env["containerCode"])
            status = status_map.get(code, 3)

            # 状态统计
            if status == 0:
                stats["opened"] += 1
            elif status == 1:
                stats["opening"] += 1
            elif status == 2:
                stats["closing"] += 1
            else:
                stats["closed"] += 1

            # 分组统计
            group = env.get("tagName", "未分组")
            stats["by_group"][group] = stats["by_group"].get(group, 0) + 1

            # 代理类型统计
            proxy_type = env.get("proxyTypeName", "未知")
            stats["by_proxy_type"][proxy_type] = stats["by_proxy_type"].get(proxy_type, 0) + 1

        return stats

# 使用示例
def main():
    manager = HubstudioManager()

    # 登录
    if not manager.login("your_app_id", "your_app_secret", "your_group_code"):
        print("登录失败")
        return

    # 获取统计信息
    stats = manager.get_environment_statistics()
    print("=== 环境统计信息 ===")
    print(f"总环境数: {stats['total']}")
    print(f"已开启: {stats['opened']}")
    print(f"开启中: {stats['opening']}")
    print(f"关闭中: {stats['closing']}")
    print(f"已关闭: {stats['closed']}")

    print("\n=== 分组统计 ===")
    for group, count in stats['by_group'].items():
        print(f"{group}: {count}")

    print("\n=== 代理类型统计 ===")
    for proxy_type, count in stats['by_proxy_type'].items():
        print(f"{proxy_type}: {count}")

if __name__ == "__main__":
    main()
```

### 6.2 环境监控和自动重启
```python
import time
import schedule
from datetime import datetime

class EnvironmentMonitor:
    def __init__(self, manager: HubstudioManager):
        self.manager = manager
        self.target_environments = []  # 需要保持运行的环境ID列表

    def add_target_environment(self, container_code: str):
        """添加需要监控的环境"""
        if container_code not in self.target_environments:
            self.target_environments.append(container_code)

    def check_and_restart(self):
        """检查并重启异常环境"""
        if not self.target_environments:
            return

        status_map = self.manager.get_environment_status(self.target_environments)

        for container_code in self.target_environments:
            status = status_map.get(container_code, 3)

            # 如果环境已关闭，尝试重启
            if status == 3:
                print(f"[{datetime.now()}] 检测到环境 {container_code} 已关闭，尝试重启...")
                result = self.manager.start_environment(container_code)
                if result:
                    print(f"环境 {container_code} 重启成功")
                else:
                    print(f"环境 {container_code} 重启失败")

                # 重启间隔
                time.sleep(5)

    def start_monitoring(self, interval_minutes: int = 5):
        """开始监控"""
        schedule.every(interval_minutes).minutes.do(self.check_and_restart)

        print(f"开始监控 {len(self.target_environments)} 个环境，检查间隔: {interval_minutes} 分钟")

        while True:
            schedule.run_pending()
            time.sleep(60)

# 使用示例
def setup_monitoring():
    manager = HubstudioManager()
    if manager.login("your_app_id", "your_app_secret", "your_group_code"):
        monitor = EnvironmentMonitor(manager)

        # 添加需要监控的环境
        monitor.add_target_environment("123456789")
        monitor.add_target_environment("987654321")

        # 开始监控（每5分钟检查一次）
        monitor.start_monitoring(5)
```

### 6.3 环境性能优化
```python
def optimize_environment_usage(manager: HubstudioManager, max_concurrent: int = 10):
    """优化环境使用，控制并发数量"""

    # 获取所有环境状态
    environments = manager.get_all_environments()
    container_codes = [str(env["containerCode"]) for env in environments]
    status_map = manager.get_environment_status(container_codes)

    # 统计当前运行的环境
    running_envs = [code for code, status in status_map.items() if status in [0, 1]]

    print(f"当前运行环境数: {len(running_envs)}")
    print(f"最大并发限制: {max_concurrent}")

    # 如果超过限制，关闭一些环境
    if len(running_envs) > max_concurrent:
        excess_count = len(running_envs) - max_concurrent
        envs_to_close = running_envs[:excess_count]

        print(f"需要关闭 {excess_count} 个环境以优化性能")

        for env_code in envs_to_close:
            if manager.stop_environment(env_code):
                print(f"已关闭环境: {env_code}")
            time.sleep(1)  # 避免过快操作
```

## 7. 总结

通过Hubstudio的API接口，您可以：
- 获取环境总数和详细信息
- 监控环境运行状态
- 批量管理环境（启动、关闭、更新）
- 自动化环境操作流程
- 实现环境监控和自动重启
- 优化环境资源使用

这些功能使得大规模环境管理变得高效和可控，特别适合需要管理大量账号的业务场景。

## 8. 注意事项

1. **API限制**：避免过于频繁的API调用，建议在操作间添加适当延迟
2. **资源管理**：根据系统配置合理控制同时运行的环境数量
3. **错误处理**：实现完善的错误处理和重试机制
4. **安全性**：妥善保管API凭证，避免泄露
5. **版本兼容**：确保客户端版本支持所使用的API功能

## 9. 相关资源

- **官方网站**：https://www.hubstudio.cn/
- **客户端下载**：https://www.hubstudio.cn/download/
- **技术支持**：13305912638 / 18059169535
- **帮助文档**：https://support-orig.hubstudio.cn/
