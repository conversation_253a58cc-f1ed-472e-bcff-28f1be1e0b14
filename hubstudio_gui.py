#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hubstudio GUI自动化界面
提供图形化界面来控制Hubstudio自动化流程
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext, simpledialog
import threading
import json
import os
from datetime import datetime
from hubstudio_complete_automation import HubstudioCompleteAutomation
from gui_dialogs import FileUploadDialog, EnvironmentSelectorDialog, SettingsDialog

class HubstudioGUI:
    """Hubstudio GUI主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Hubstudio 自动化控制系统")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化变量
        self.automation = None
        self.config = {}
        self.environments = []
        self.is_running = False
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置
        self.load_config()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置颜色
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Hubstudio 自动化控制系统", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 配置区域
        self.create_config_section(main_frame, 1)
        
        # 控制区域
        self.create_control_section(main_frame, 2)
        
        # 环境列表区域
        self.create_environment_section(main_frame, 3)
        
        # 日志区域
        self.create_log_section(main_frame, 4)
        
        # 状态栏
        self.create_status_bar(main_frame, 5)
        
    def create_config_section(self, parent, row):
        """创建配置区域"""
        config_frame = ttk.LabelFrame(parent, text="配置设置", padding="10")
        config_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # Hubstudio路径
        ttk.Label(config_frame, text="Hubstudio路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.hubstudio_path_var = tk.StringVar()
        path_entry = ttk.Entry(config_frame, textvariable=self.hubstudio_path_var, width=50)
        path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(config_frame, text="浏览", command=self.browse_hubstudio_path).grid(row=0, column=2)
        
        # API配置
        ttk.Label(config_frame, text="APP ID:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.app_id_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_id_var, width=30).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        ttk.Label(config_frame, text="APP Secret:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.app_secret_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_secret_var, width=30, show="*").grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        ttk.Label(config_frame, text="Group Code:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.group_code_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.group_code_var, width=30).grid(row=3, column=1, sticky=tk.W, pady=(5, 0))
        
        # 保存配置按钮
        ttk.Button(config_frame, text="保存配置", command=self.save_config).grid(row=4, column=1, sticky=tk.W, pady=(10, 0))
        
    def create_control_section(self, parent, row):
        """创建控制区域"""
        control_frame = ttk.LabelFrame(parent, text="控制操作", padding="10")
        control_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 主要控制按钮
        self.start_btn = ttk.Button(button_frame, text="🚀 启动完整流程", command=self.start_automation)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_frame, text="🛑 停止", command=self.stop_automation, state='disabled')
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(button_frame, text="🔄 刷新环境", command=self.refresh_environments).grid(row=0, column=2, padx=(0, 10))
        
        ttk.Button(button_frame, text="🧹 清理资源", command=self.cleanup_resources).grid(row=0, column=3, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        control_frame.columnconfigure(0, weight=1)
        
    def create_environment_section(self, parent, row):
        """创建环境列表区域"""
        env_frame = ttk.LabelFrame(parent, text="浏览器环境", padding="10")
        env_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        env_frame.columnconfigure(0, weight=1)
        env_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ('ID', '名称', '状态', '代理类型', '最后使用IP')
        self.env_tree = ttk.Treeview(env_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        for col in columns:
            self.env_tree.heading(col, text=col)
            self.env_tree.column(col, width=120)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(env_frame, orient=tk.VERTICAL, command=self.env_tree.yview)
        self.env_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.env_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 环境操作按钮
        env_btn_frame = ttk.Frame(env_frame)
        env_btn_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(env_btn_frame, text="打开选中环境", command=self.open_selected_environment).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(env_btn_frame, text="关闭选中环境", command=self.close_selected_environment).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(env_btn_frame, text="文件上传", command=self.upload_file_dialog).grid(row=0, column=2, padx=(0, 10))
        ttk.Button(env_btn_frame, text="高级设置", command=self.show_settings).grid(row=0, column=3)
        
    def create_log_section(self, parent, row):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="10")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.grid(row=1, column=0, pady=(10, 0))
        
        ttk.Button(log_btn_frame, text="清空日志", command=self.clear_log).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(log_btn_frame, text="保存日志", command=self.save_log).grid(row=0, column=1)
        
    def create_status_bar(self, parent, row):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 更新状态栏
        self.status_var.set(message)
        
        # 刷新界面
        self.root.update_idletasks()
        
    def browse_hubstudio_path(self):
        """浏览Hubstudio安装路径"""
        filename = filedialog.askopenfilename(
            title="选择Hubstudio.exe",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.hubstudio_path_var.set(filename)
            
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists("hubstudio_config.json"):
                with open("hubstudio_config.json", 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                
                # 填充界面
                hubstudio_config = self.config.get("hubstudio", {})
                api_config = self.config.get("api", {})
                
                self.hubstudio_path_var.set(hubstudio_config.get("install_path", ""))
                self.app_id_var.set(api_config.get("app_id", ""))
                self.app_secret_var.set(api_config.get("app_secret", ""))
                self.group_code_var.set(api_config.get("group_code", ""))
                
                self.log_message("配置文件加载成功")
            else:
                self.log_message("配置文件不存在，使用默认配置", "WARNING")
        except Exception as e:
            self.log_message(f"加载配置失败: {e}", "ERROR")
            
    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                "hubstudio": {
                    "install_path": self.hubstudio_path_var.get(),
                    "startup_timeout": 60,
                    "api_port": 6873
                },
                "api": {
                    "app_id": self.app_id_var.get(),
                    "app_secret": self.app_secret_var.get(),
                    "group_code": self.group_code_var.get()
                },
                "automation": {
                    "wait_after_startup": 5,
                    "default_startup_urls": ["https://www.baidu.com"]
                }
            }
            
            with open("hubstudio_config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            self.config = config
            self.log_message("配置保存成功")
            messagebox.showinfo("成功", "配置保存成功！")
            
        except Exception as e:
            self.log_message(f"保存配置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"保存配置失败: {e}")
            
    def start_automation(self):
        """启动自动化流程"""
        if self.is_running:
            messagebox.showwarning("警告", "自动化流程正在运行中")
            return
            
        # 验证配置
        if not all([self.app_id_var.get(), self.app_secret_var.get(), self.group_code_var.get()]):
            messagebox.showerror("错误", "请先完整填写API配置信息")
            return
            
        # 在新线程中运行自动化
        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        
        thread = threading.Thread(target=self._run_automation)
        thread.daemon = True
        thread.start()
        
    def _run_automation(self):
        """在后台线程中运行自动化"""
        try:
            self.log_message("开始启动自动化流程...")
            self.progress_var.set(10)
            
            # 创建自动化控制器
            self.automation = HubstudioCompleteAutomation("hubstudio_config.json")
            
            # 第一步：启动Hubstudio软件
            self.log_message("正在启动Hubstudio软件...")
            self.progress_var.set(20)
            if not self.automation.start_hubstudio_software():
                raise Exception("Hubstudio软件启动失败")
                
            # 第二步：登录API
            self.log_message("正在登录API...")
            self.progress_var.set(40)
            if not self.automation.login_api():
                raise Exception("API登录失败")
                
            # 第三步：获取环境列表
            self.log_message("正在获取环境列表...")
            self.progress_var.set(60)
            self.environments = self.automation.get_browser_environments()
            if not self.environments:
                raise Exception("没有可用的浏览器环境")
                
            # 更新环境列表显示
            self.root.after(0, self.update_environment_list)
            
            # 第四步：打开第一个环境
            self.log_message("正在打开浏览器环境...")
            self.progress_var.set(80)
            first_env = self.environments[0]
            container_code = str(first_env["containerCode"])
            
            if self.automation.open_browser_environment(container_code):
                self.log_message(f"浏览器环境 {first_env.get('containerName')} 打开成功")
                self.progress_var.set(100)
                self.log_message("自动化流程完成！浏览器环境已打开，可以进行手动操作")
            else:
                raise Exception("浏览器环境打开失败")
                
        except Exception as e:
            self.log_message(f"自动化流程失败: {e}", "ERROR")
            self.root.after(0, lambda: messagebox.showerror("错误", f"自动化流程失败: {e}"))
        finally:
            self.is_running = False
            self.root.after(0, self._automation_finished)
            
    def _automation_finished(self):
        """自动化完成后的界面更新"""
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_var.set(0)
        
    def stop_automation(self):
        """停止自动化流程"""
        self.is_running = False
        self.log_message("正在停止自动化流程...")
        if self.automation:
            self.automation.cleanup()
        self._automation_finished()
        
    def update_environment_list(self):
        """更新环境列表显示"""
        # 清空现有项目
        for item in self.env_tree.get_children():
            self.env_tree.delete(item)
            
        # 添加环境信息
        for env in self.environments:
            self.env_tree.insert('', 'end', values=(
                env.get('containerCode', ''),
                env.get('containerName', ''),
                '未知',  # 状态需要单独查询
                env.get('proxyTypeName', ''),
                env.get('lastUsedIp', '')
            ))
            
    def refresh_environments(self):
        """刷新环境列表"""
        if not self.automation:
            messagebox.showwarning("警告", "请先启动自动化流程")
            return
            
        try:
            self.log_message("正在刷新环境列表...")
            self.environments = self.automation.get_browser_environments()
            self.update_environment_list()
            self.log_message("环境列表刷新完成")
        except Exception as e:
            self.log_message(f"刷新环境列表失败: {e}", "ERROR")
            
    def open_selected_environment(self):
        """打开选中的环境"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个环境")
            return
            
        if not self.automation:
            messagebox.showwarning("警告", "请先启动自动化流程")
            return
            
        item = self.env_tree.item(selection[0])
        container_code = item['values'][0]
        env_name = item['values'][1]
        
        try:
            self.log_message(f"正在打开环境: {env_name}")
            if self.automation.open_browser_environment(str(container_code)):
                self.log_message(f"环境 {env_name} 打开成功")
                messagebox.showinfo("成功", f"环境 {env_name} 打开成功！")
            else:
                raise Exception("环境打开失败")
        except Exception as e:
            self.log_message(f"打开环境失败: {e}", "ERROR")
            messagebox.showerror("错误", f"打开环境失败: {e}")
            
    def close_selected_environment(self):
        """关闭选中的环境"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个环境")
            return
            
        item = self.env_tree.item(selection[0])
        container_code = item['values'][0]
        env_name = item['values'][1]
        
        # 这里需要调用关闭环境的API
        self.log_message(f"关闭环境功能待实现: {env_name}")
        
    def upload_file_dialog(self):
        """文件上传对话框"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个环境")
            return

        if not self.automation:
            messagebox.showwarning("警告", "请先启动自动化流程")
            return

        # 显示文件上传对话框
        dialog = FileUploadDialog(self.root)
        result = dialog.show()

        if result:
            # 获取选中的环境
            item = self.env_tree.item(selection[0])
            container_code = item['values'][0]
            env_name = item['values'][1]

            # 在新线程中执行上传
            thread = threading.Thread(target=self._upload_file_thread,
                                     args=(container_code, env_name, result))
            thread.daemon = True
            thread.start()
                
    def _upload_file_thread(self, container_code, env_name, upload_config):
        """在后台线程中执行文件上传"""
        try:
            self.log_message(f"开始在环境 {env_name} 中上传文件...")

            # 连接到浏览器
            driver = self.automation.connect_to_browser(container_code)
            if not driver:
                raise Exception("无法连接到浏览器环境")

            # 执行上传
            self.automation.upload_file_example(
                driver,
                upload_config['file_path'],
                upload_config['url']
            )

            self.log_message(f"文件上传完成: {os.path.basename(upload_config['file_path'])}")

            # 关闭WebDriver连接
            driver.quit()

        except Exception as e:
            self.log_message(f"文件上传失败: {e}", "ERROR")
            self.root.after(0, lambda: messagebox.showerror("错误", f"文件上传失败: {e}"))

    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self.root, self.config)
        result = dialog.show()

        if result:
            self.config = result
            self.save_config()
            self.log_message("设置已更新")

    def cleanup_resources(self):
        """清理资源"""
        if self.automation:
            self.automation.cleanup()
            self.log_message("资源清理完成")
        else:
            self.log_message("没有需要清理的资源")
            
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", "日志保存成功！")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")
                
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = HubstudioGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")


if __name__ == "__main__":
    main()
