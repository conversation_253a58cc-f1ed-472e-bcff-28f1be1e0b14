#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Hubstudio浏览器环境打开工具
专门用于打开浏览器环境并进行文件上传操作
"""

import requests
import time
import json
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SimpleBrowserOpener:
    """简化的浏览器环境打开器"""
    
    def __init__(self, base_url="http://localhost:6873"):
        self.base_url = base_url
        self.headers = {"Content-Type": "application/json"}
        self.session = requests.Session()
        self.opened_environments = {}  # 存储已打开的环境信息
    
    def login(self, app_id: str, app_secret: str, group_code: str) -> bool:
        """登录Hubstudio API"""
        url = f"{self.base_url}/login"
        data = {
            "appId": app_id,
            "appSecret": app_secret,
            "groupCode": group_code
        }
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                print("✅ API登录成功")
                return True
            else:
                print(f"❌ API登录失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_environment_list(self) -> List[Dict]:
        """获取环境列表"""
        url = f"{self.base_url}/api/v1/env/list"
        data = {"current": 1, "size": 200}
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                environments = result["data"]["list"]
                print(f"📋 获取到 {len(environments)} 个浏览器环境")
                return environments
            else:
                print(f"❌ 获取环境列表失败: {result.get('msg')}")
                return []
                
        except Exception as e:
            print(f"❌ 获取环境列表异常: {e}")
            return []
    
    def open_browser_environment(self, container_code: str, 
                                startup_urls: List[str] = None) -> Optional[Dict]:
        """打开指定的浏览器环境"""
        url = f"{self.base_url}/api/v1/browser/start"
        data = {
            "containerCode": container_code,
            "isHeadless": False,  # 显示浏览器窗口
            "isWebDriverReadOnlyMode": False
        }
        
        # 如果指定了启动URL，添加到请求中
        if startup_urls:
            data["containerTabs"] = startup_urls
        
        try:
            print(f"🚀 正在打开环境 {container_code}...")
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                browser_info = result["data"]
                print(f"✅ 环境 {container_code} 打开成功")
                print(f"   调试端口: {browser_info.get('debuggingPort')}")
                print(f"   浏览器ID: {browser_info.get('browserID')}")
                
                # 保存环境信息
                self.opened_environments[container_code] = browser_info
                return browser_info
            else:
                print(f"❌ 打开环境 {container_code} 失败: {result.get('msg')}")
                return None
                
        except Exception as e:
            print(f"❌ 打开环境 {container_code} 异常: {e}")
            return None
    
    def connect_to_browser(self, container_code: str) -> Optional[webdriver.Chrome]:
        """连接到已打开的浏览器环境"""
        if container_code not in self.opened_environments:
            print(f"❌ 环境 {container_code} 未打开")
            return None
        
        browser_info = self.opened_environments[container_code]
        debug_port = browser_info.get("debuggingPort")
        
        if not debug_port:
            print("❌ 未获取到调试端口")
            return None
        
        try:
            # 配置Chrome选项连接到现有浏览器
            options = Options()
            options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            # 创建WebDriver连接
            driver = webdriver.Chrome(options=options)
            print(f"✅ 成功连接到环境 {container_code}")
            
            return driver
            
        except Exception as e:
            print(f"❌ 连接环境 {container_code} 失败: {e}")
            return None
    
    def upload_file_to_website(self, driver: webdriver.Chrome, 
                              website_url: str, file_path: str,
                              upload_selector: str = "input[type='file']") -> bool:
        """在指定网站上传文件"""
        try:
            print(f"📁 准备上传文件: {file_path}")
            print(f"🌐 目标网站: {website_url}")
            
            # 访问网站
            driver.get(website_url)
            
            # 等待页面加载
            wait = WebDriverWait(driver, 10)
            
            # 查找文件上传元素
            upload_element = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, upload_selector))
            )
            
            # 上传文件
            upload_element.send_keys(file_path)
            print(f"✅ 文件上传成功")
            
            # 等待一下让用户看到结果
            time.sleep(2)
            
            return True
            
        except Exception as e:
            print(f"❌ 文件上传失败: {e}")
            return False
    
    def close_environment(self, container_code: str) -> bool:
        """关闭指定环境"""
        url = f"{self.base_url}/api/v1/browser/stop"
        data = {"containerCode": container_code}
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                print(f"✅ 环境 {container_code} 关闭成功")
                # 从已打开环境中移除
                if container_code in self.opened_environments:
                    del self.opened_environments[container_code]
                return True
            else:
                print(f"❌ 关闭环境 {container_code} 失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            print(f"❌ 关闭环境 {container_code} 异常: {e}")
            return False
    
    def close_all_environments(self) -> bool:
        """关闭所有已打开的环境"""
        url = f"{self.base_url}/api/v1/browser/stop-all"
        data = {"clearOpening": True}
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                print("✅ 所有环境已关闭")
                self.opened_environments.clear()
                return True
            else:
                print(f"❌ 关闭所有环境失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            print(f"❌ 关闭所有环境异常: {e}")
            return False
    
    def list_opened_environments(self):
        """列出当前已打开的环境"""
        if not self.opened_environments:
            print("📋 当前没有已打开的环境")
            return
        
        print("📋 当前已打开的环境:")
        for container_code, info in self.opened_environments.items():
            debug_port = info.get('debuggingPort', 'N/A')
            browser_id = info.get('browserID', 'N/A')
            print(f"  🌐 环境ID: {container_code}")
            print(f"     调试端口: {debug_port}")
            print(f"     浏览器ID: {browser_id}")
            print()


def main():
    """主函数 - 演示简单的浏览器打开和文件上传"""
    
    print("🚀 Hubstudio 简单浏览器环境打开工具")
    print("=" * 50)
    
    # 配置信息（请替换为您的实际凭证）
    API_CONFIG = {
        "app_id": "your_app_id",
        "app_secret": "your_app_secret", 
        "group_code": "your_group_code"
    }
    
    # 创建浏览器打开器
    opener = SimpleBrowserOpener()
    
    try:
        # 1. 登录API
        if not opener.login(**API_CONFIG):
            print("❌ API登录失败，程序退出")
            return
        
        # 2. 获取环境列表
        environments = opener.get_environment_list()
        if not environments:
            print("❌ 没有可用的浏览器环境")
            return
        
        # 3. 显示前5个环境供选择
        print("\n📋 可用的浏览器环境:")
        for i, env in enumerate(environments[:5]):
            print(f"  {i+1}. {env['containerName']} (ID: {env['containerCode']})")
        
        # 4. 选择要打开的环境（这里选择第一个）
        selected_env = environments[0]
        container_code = str(selected_env["containerCode"])
        
        print(f"\n🎯 选择环境: {selected_env['containerName']} ({container_code})")
        
        # 5. 打开浏览器环境
        browser_info = opener.open_browser_environment(
            container_code,
            startup_urls=["https://www.baidu.com"]  # 可以指定启动时打开的网页
        )
        
        if not browser_info:
            print("❌ 环境打开失败")
            return
        
        # 6. 等待环境完全启动
        print("⏳ 等待环境完全启动...")
        time.sleep(5)
        
        # 7. 连接到浏览器（可选，如果需要自动化控制）
        print("\n🔗 连接到浏览器进行自动化控制...")
        driver = opener.connect_to_browser(container_code)
        
        if driver:
            print("✅ 浏览器连接成功，可以进行自动化操作")
            
            # 示例：访问一个支持文件上传的网站
            # 注意：这里只是演示，实际使用时请替换为您需要的网站和文件路径
            """
            file_path = r"C:\path\to\your\file.txt"  # 替换为实际文件路径
            upload_success = opener.upload_file_to_website(
                driver, 
                "https://example.com/upload",  # 替换为实际上传网站
                file_path,
                "input[type='file']"  # 文件上传元素的CSS选择器
            )
            """
            
            # 关闭WebDriver连接（浏览器环境仍然保持打开）
            driver.quit()
            print("✅ WebDriver连接已关闭，浏览器环境仍在运行")
        
        # 8. 列出当前打开的环境
        opener.list_opened_environments()
        
        # 9. 询问是否关闭环境
        print("\n" + "=" * 50)
        print("浏览器环境已打开，您可以:")
        print("1. 手动在浏览器中进行操作")
        print("2. 保持环境打开状态")
        print("3. 关闭环境")
        
        choice = input("\n是否关闭环境？(y/n): ").lower().strip()
        
        if choice == 'y':
            opener.close_environment(container_code)
        else:
            print("✅ 环境保持打开状态，您可以手动操作")
            print(f"   环境ID: {container_code}")
            print(f"   调试端口: {browser_info.get('debuggingPort')}")
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
    finally:
        print("\n🧹 程序结束")


if __name__ == "__main__":
    main()
