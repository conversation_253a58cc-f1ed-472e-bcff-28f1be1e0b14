#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Hubstudio自动化脚本
专注于打开浏览器环境并执行上传操作
"""

import requests
import time
import json
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SimpleHubstudioController:
    """简化的Hubstudio控制器"""
    
    def __init__(self, base_url="http://localhost:6873"):
        self.base_url = base_url
        self.headers = {"Content-Type": "application/json"}
        self.session = requests.Session()
        self.logged_in = False
        self.active_drivers = {}  # 存储活跃的WebDriver实例
    
    def login(self, app_id: str, app_secret: str, group_code: str) -> bool:
        """登录Hubstudio"""
        url = f"{self.base_url}/login"
        data = {
            "appId": app_id,
            "appSecret": app_secret,
            "groupCode": group_code
        }
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                self.logged_in = True
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_environment_list(self) -> List[Dict]:
        """获取环境列表"""
        if not self.logged_in:
            print("❌ 请先登录")
            return []
        
        url = f"{self.base_url}/api/v1/env/list"
        data = {"current": 1, "size": 200}
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                environments = result["data"]["list"]
                print(f"📋 获取到 {len(environments)} 个环境")
                return environments
            else:
                print(f"❌ 获取环境列表失败: {result.get('msg')}")
                return []
                
        except Exception as e:
            print(f"❌ 获取环境列表异常: {e}")
            return []
    
    def start_environment(self, container_code: str, urls: List[str] = None) -> Optional[Dict]:
        """启动浏览器环境"""
        if not self.logged_in:
            print("❌ 请先登录")
            return None
        
        url = f"{self.base_url}/api/v1/browser/start"
        data = {
            "containerCode": container_code,
            "isHeadless": False,  # 显示浏览器窗口
            "isWebDriverReadOnlyMode": False
        }
        
        if urls:
            data["containerTabs"] = urls
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                browser_info = result["data"]
                print(f"✅ 环境 {container_code} 启动成功")
                print(f"   调试端口: {browser_info.get('debuggingPort')}")
                return browser_info
            else:
                print(f"❌ 启动环境 {container_code} 失败: {result.get('msg')}")
                return None
                
        except Exception as e:
            print(f"❌ 启动环境 {container_code} 异常: {e}")
            return None
    
    def connect_to_environment(self, browser_info: Dict) -> Optional[webdriver.Chrome]:
        """连接到已启动的浏览器环境"""
        try:
            debug_port = browser_info.get("debuggingPort")
            if not debug_port:
                print("❌ 未获取到调试端口")
                return None
            
            # 配置Chrome选项
            options = Options()
            options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            # 创建WebDriver连接
            driver = webdriver.Chrome(options=options)
            print(f"✅ 成功连接到浏览器环境，调试端口: {debug_port}")
            
            return driver
            
        except Exception as e:
            print(f"❌ 连接浏览器环境失败: {e}")
            return None
    
    def open_environment_and_connect(self, container_code: str, 
                                   startup_urls: List[str] = None) -> Optional[webdriver.Chrome]:
        """打开环境并连接WebDriver"""
        print(f"🚀 正在启动环境 {container_code}...")
        
        # 1. 启动环境
        browser_info = self.start_environment(container_code, startup_urls)
        if not browser_info:
            return None
        
        # 2. 等待环境完全启动
        print("⏳ 等待环境启动完成...")
        time.sleep(5)
        
        # 3. 连接WebDriver
        driver = self.connect_to_environment(browser_info)
        if driver:
            self.active_drivers[container_code] = driver
            print(f"✅ 环境 {container_code} 已就绪，可以开始操作")
        
        return driver
    
    def upload_file_to_page(self, driver: webdriver.Chrome, file_path: str, 
                          upload_selector: str = "input[type='file']") -> bool:
        """在页面中上传文件"""
        try:
            print(f"📤 开始上传文件: {file_path}")
            
            # 等待文件上传元素出现
            wait = WebDriverWait(driver, 10)
            file_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, upload_selector)))
            
            # 上传文件
            file_input.send_keys(file_path)
            print("✅ 文件上传成功")
            
            return True
            
        except Exception as e:
            print(f"❌ 文件上传失败: {e}")
            return False
    
    def batch_upload_files(self, driver: webdriver.Chrome, file_paths: List[str],
                          upload_selector: str = "input[type='file']", 
                          delay: int = 2) -> Dict[str, bool]:
        """批量上传文件"""
        results = {}
        
        for i, file_path in enumerate(file_paths):
            print(f"📤 上传文件 {i+1}/{len(file_paths)}: {file_path}")
            
            success = self.upload_file_to_page(driver, file_path, upload_selector)
            results[file_path] = success
            
            # 上传间隔
            if i < len(file_paths) - 1:
                time.sleep(delay)
        
        return results
    
    def close_environment(self, container_code: str) -> bool:
        """关闭浏览器环境"""
        if not self.logged_in:
            print("❌ 请先登录")
            return False
        
        # 先关闭WebDriver连接
        if container_code in self.active_drivers:
            try:
                self.active_drivers[container_code].quit()
                del self.active_drivers[container_code]
                print(f"✅ 已关闭环境 {container_code} 的WebDriver连接")
            except:
                pass
        
        # 通过API关闭环境
        url = f"{self.base_url}/api/v1/browser/stop"
        data = {"containerCode": container_code}
        
        try:
            response = self.session.post(url, headers=self.headers, json=data)
            result = response.json()
            
            if result.get("code") == 0:
                print(f"✅ 环境 {container_code} 关闭成功")
                return True
            else:
                print(f"❌ 关闭环境 {container_code} 失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            print(f"❌ 关闭环境 {container_code} 异常: {e}")
            return False
    
    def cleanup_all(self):
        """清理所有资源"""
        print("🧹 开始清理资源...")
        
        # 关闭所有WebDriver
        for container_code, driver in list(self.active_drivers.items()):
            try:
                driver.quit()
                print(f"✅ 已关闭环境 {container_code} 的WebDriver")
            except:
                pass
        
        self.active_drivers.clear()
        
        # 关闭所有环境
        try:
            url = f"{self.base_url}/api/v1/browser/stop-all"
            data = {"clearOpening": True}
            self.session.post(url, headers=self.headers, json=data)
            print("✅ 已关闭所有浏览器环境")
        except:
            pass
        
        print("✅ 资源清理完成")


def upload_demo(driver: webdriver.Chrome, file_paths: List[str]):
    """上传演示函数"""
    try:
        # 示例：访问一个支持文件上传的页面
        driver.get("https://www.w3schools.com/html/html_forms.asp")
        
        # 等待页面加载
        time.sleep(3)
        
        # 查找文件上传元素（这里只是示例，实际使用时需要根据具体页面调整）
        file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
        
        if file_inputs:
            print("📤 找到文件上传元素，开始上传...")
            
            for i, file_path in enumerate(file_paths):
                if i < len(file_inputs):
                    try:
                        file_inputs[i].send_keys(file_path)
                        print(f"✅ 文件 {file_path} 上传成功")
                        time.sleep(1)
                    except Exception as e:
                        print(f"❌ 文件 {file_path} 上传失败: {e}")
        else:
            print("❌ 未找到文件上传元素")
            
    except Exception as e:
        print(f"❌ 上传演示失败: {e}")


def main():
    """主函数 - 简化的使用示例"""
    
    # 配置信息（请替换为您的实际凭证）
    CONFIG = {
        "app_id": "your_app_id",
        "app_secret": "your_app_secret", 
        "group_code": "your_group_code"
    }
    
    # 要上传的文件路径列表（请替换为实际文件路径）
    FILE_PATHS = [
        r"C:\path\to\your\file1.jpg",
        r"C:\path\to\your\file2.png",
        r"C:\path\to\your\file3.pdf"
    ]
    
    print("🚀 Hubstudio 简化自动化 - 环境打开和文件上传")
    print("-" * 50)
    
    # 创建控制器
    controller = SimpleHubstudioController()
    
    try:
        # 1. 登录
        if not controller.login(CONFIG["app_id"], CONFIG["app_secret"], CONFIG["group_code"]):
            print("❌ 登录失败，程序退出")
            return
        
        # 2. 获取环境列表
        environments = controller.get_environment_list()
        if not environments:
            print("❌ 没有可用的浏览器环境")
            return
        
        # 3. 显示可用环境
        print("\n📋 可用环境列表:")
        for i, env in enumerate(environments[:5]):  # 只显示前5个
            print(f"  {i+1}. {env['containerName']} (ID: {env['containerCode']})")
        
        # 4. 选择要使用的环境（这里选择第一个）
        target_env = environments[0]
        container_code = str(target_env["containerCode"])
        
        print(f"\n🎯 选择环境: {target_env['containerName']} ({container_code})")
        
        # 5. 打开环境并连接
        driver = controller.open_environment_and_connect(
            container_code, 
            startup_urls=["https://www.baidu.com"]  # 可选：启动时打开的页面
        )
        
        if not driver:
            print("❌ 环境启动失败")
            return
        
        # 6. 执行上传操作（这里是演示，实际使用时请根据具体需求修改）
        print("\n📤 开始文件上传演示...")
        upload_demo(driver, FILE_PATHS)
        
        # 7. 等待用户操作（可选）
        print("\n⏳ 环境已就绪，您可以手动操作浏览器...")
        print("按回车键继续...")
        input()
        
        # 8. 关闭环境
        print(f"\n🔒 关闭环境 {container_code}")
        controller.close_environment(container_code)
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行异常: {e}")
    finally:
        # 9. 清理所有资源
        controller.cleanup_all()
        print("\n✅ 程序执行完成")


if __name__ == "__main__":
    main()
