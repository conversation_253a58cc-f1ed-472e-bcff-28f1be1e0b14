# Hubstudio 完整自动化流程使用说明

## 🎯 正确的流程说明

这个工具实现了您需要的完整流程：

1. **自动启动Hubstudio软件** 🚀
2. **等待软件完全启动** ⏳
3. **通过API打开软件内的浏览器环境** 🌐
4. **进行文件上传等其他操作** 📤

## 📁 文件说明

- **`hubstudio_complete_automation.py`** - 核心自动化控制器
- **`hubstudio_config.json`** - 配置文件
- **`simple_usage_example.py`** - 使用示例
- **`正确的使用说明.md`** - 本说明文档

## 🚀 快速开始

### 第一步：配置

编辑 `hubstudio_config.json` 文件：

```json
{
    "hubstudio": {
        "install_path": "",  // 留空自动检测，或填入完整路径
        "startup_timeout": 60,
        "api_port": 6873
    },
    "api": {
        "app_id": "您的APP_ID",
        "app_secret": "您的APP_SECRET", 
        "group_code": "您的团队代码"
    },
    "automation": {
        "wait_after_startup": 5,
        "default_startup_urls": ["https://www.baidu.com"]
    }
}
```

### 第二步：安装依赖

```bash
pip install requests selenium psutil
```

### 第三步：运行

#### 方式1：运行完整流程（推荐）

```bash
python hubstudio_complete_automation.py
```

这会自动执行：
1. ✅ 启动Hubstudio软件
2. ✅ 等待软件启动完成
3. ✅ 登录API
4. ✅ 获取环境列表
5. ✅ 打开第一个环境
6. ✅ 显示浏览器窗口供您操作

#### 方式2：运行示例程序

```bash
python simple_usage_example.py
```

提供多种使用示例：
- 基础使用
- 包含文件上传
- 分步骤执行
- 多环境打开

## 🔧 详细功能说明

### HubstudioCompleteAutomation 类

#### 核心方法：

1. **`start_hubstudio_software()`** - 启动Hubstudio软件
   - 自动检测安装路径
   - 检查是否已运行
   - 启动软件进程
   - 等待API服务就绪

2. **`login_api()`** - 登录API服务
   - 使用配置的凭证登录
   - 验证登录状态

3. **`get_browser_environments()`** - 获取环境列表
   - 获取所有可用的浏览器环境
   - 显示环境信息

4. **`open_browser_environment(container_code)`** - 打开浏览器环境
   - 打开指定ID的环境
   - 可指定启动URL
   - 返回环境信息

5. **`connect_to_browser(container_code)`** - 连接浏览器进行自动化
   - 通过调试端口连接
   - 返回WebDriver实例

6. **`run_complete_automation()`** - 运行完整流程
   - 一键执行所有步骤
   - 可选文件上传功能

## 💡 使用示例

### 示例1：最简单的使用

```python
from hubstudio_complete_automation import HubstudioCompleteAutomation

# 创建控制器
automation = HubstudioCompleteAutomation()

# 运行完整流程
success = automation.run_complete_automation()

if success:
    print("✅ 流程完成，浏览器环境已打开")
    input("按回车键关闭...")

automation.cleanup()
```

### 示例2：分步骤控制

```python
automation = HubstudioCompleteAutomation()

# 第一步：启动Hubstudio软件
automation.start_hubstudio_software()

# 第二步：登录API
automation.login_api()

# 第三步：获取环境列表
environments = automation.get_browser_environments()

# 第四步：打开指定环境
container_code = str(environments[0]["containerCode"])
automation.open_browser_environment(container_code)

# 第五步：连接浏览器进行自动化（可选）
driver = automation.connect_to_browser(container_code)
if driver:
    driver.get("https://example.com")
    # 进行其他自动化操作...
    driver.quit()

automation.cleanup()
```

### 示例3：文件上传

```python
automation = HubstudioCompleteAutomation()

# 运行包含文件上传的完整流程
automation.run_complete_automation(
    file_path="C:/path/to/file.jpg",
    upload_url="https://upload-site.com"
)

automation.cleanup()
```

## 🛠️ 高级配置

### 自定义Hubstudio路径

如果自动检测失败，可以手动指定：

```json
{
    "hubstudio": {
        "install_path": "D:\\MyApps\\Hubstudio\\Hubstudio.exe"
    }
}
```

### 自定义启动URL

```json
{
    "automation": {
        "default_startup_urls": [
            "https://www.google.com",
            "https://www.facebook.com"
        ]
    }
}
```

### 调整超时时间

```json
{
    "hubstudio": {
        "startup_timeout": 120  // 增加到2分钟
    },
    "automation": {
        "wait_after_startup": 10  // 环境启动后等待10秒
    }
}
```

## 🔍 故障排除

### 问题1：找不到Hubstudio安装路径

**解决方案：**
1. 在配置文件中手动指定 `install_path`
2. 确保路径正确且文件存在
3. 检查文件权限

### 问题2：API连接失败

**解决方案：**
1. 确认Hubstudio软件已完全启动
2. 检查端口6873是否被占用
3. 验证API凭证是否正确

### 问题3：环境打开失败

**解决方案：**
1. 检查环境ID是否正确
2. 确认环境未被其他程序占用
3. 检查系统资源是否充足

### 问题4：WebDriver连接失败

**解决方案：**
1. 确认Chrome浏览器已安装
2. 检查ChromeDriver版本是否匹配
3. 验证调试端口是否正确

## 📊 执行流程图

```
开始
  ↓
检查Hubstudio是否运行
  ↓
否 → 启动Hubstudio软件
  ↓
等待API服务就绪
  ↓
登录API
  ↓
获取环境列表
  ↓
选择/指定环境
  ↓
打开浏览器环境
  ↓
等待环境启动完成
  ↓
可选：连接WebDriver进行自动化
  ↓
可选：执行文件上传等操作
  ↓
完成
```

## 🎯 核心优势

### ✅ 完全自动化
- 从软件启动到环境打开的全流程自动化
- 无需手动操作，一键完成

### ✅ 智能检测
- 自动检测Hubstudio安装路径
- 自动检测软件运行状态
- 智能等待API服务就绪

### ✅ 错误处理
- 完善的异常处理机制
- 详细的错误信息提示
- 自动资源清理

### ✅ 灵活配置
- 支持自定义配置
- 多种使用方式
- 可扩展的功能

## 📞 技术支持

如果遇到问题：
1. 检查配置文件是否正确
2. 查看控制台输出的错误信息
3. 联系Hubstudio官方技术支持

---

**这就是您需要的完整解决方案！** 🎉

从启动Hubstudio软件 → 打开浏览器环境 → 进行文件上传等操作的完整自动化流程！
