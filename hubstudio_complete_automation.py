#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hubstudio完整自动化流程
1. 自动启动Hubstudio软件
2. 等待软件完全启动
3. 通过API打开浏览器环境
4. 进行文件上传等操作
"""

import os
import sys
import time
import json
import psutil
import requests
import subprocess
from pathlib import Path
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class HubstudioCompleteAutomation:
    """Hubstudio完整自动化控制器"""
    
    def __init__(self, config_path: str = "hubstudio_config.json"):
        self.config = self._load_config(config_path)
        self.hubstudio_process = None
        self.api_ready = False
        self.opened_environments = {}
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 配置文件 {config_path} 不存在，使用默认配置")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "hubstudio": {
                "install_path": "",  # 自动检测
                "startup_timeout": 60,
                "api_port": 6873
            },
            "api": {
                "app_id": "",
                "app_secret": "",
                "group_code": ""
            },
            "automation": {
                "wait_after_startup": 5,
                "default_startup_urls": ["https://www.baidu.com"]
            }
        }
    
    def _find_hubstudio_path(self) -> str:
        """自动查找Hubstudio安装路径"""
        possible_paths = [
            r"C:\Program Files\Hubstudio\Hubstudio.exe",
            r"C:\Program Files (x86)\Hubstudio\Hubstudio.exe", 
            r"D:\Hubstudio\Hubstudio.exe",
            r"C:\Users\<USER>\AppData\Local\Hubstudio\Hubstudio.exe".format(os.getenv('USERNAME', '')),
            r"C:\Users\<USER>\Desktop\Hubstudio\Hubstudio.exe".format(os.getenv('USERNAME', '')),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"✅ 找到Hubstudio安装路径: {path}")
                return path
        
        # 尝试在常见目录中搜索
        search_dirs = [
            r"C:\Program Files",
            r"C:\Program Files (x86)",
            r"D:\",
            r"C:\Users\<USER>\Desktop".format(os.getenv('USERNAME', '')),
            r"C:\Users\<USER>\AppData\Local".format(os.getenv('USERNAME', ''))
        ]
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                for root, dirs, files in os.walk(search_dir):
                    if 'Hubstudio.exe' in files and 'Hubstudio' in root:
                        path = os.path.join(root, 'Hubstudio.exe')
                        print(f"✅ 搜索到Hubstudio安装路径: {path}")
                        return path
        
        raise FileNotFoundError("❌ 未找到Hubstudio安装路径，请手动在配置文件中指定")
    
    def _is_hubstudio_running(self) -> bool:
        """检查Hubstudio是否正在运行"""
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_info = proc.info
                if proc_info['name'] and 'hubstudio' in proc_info['name'].lower():
                    return True
                if proc_info['exe'] and 'hubstudio' in proc_info['exe'].lower():
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        return False
    
    def start_hubstudio_software(self) -> bool:
        """启动Hubstudio软件"""
        print("🚀 第一步：启动Hubstudio软件...")
        
        # 检查是否已经运行
        if self._is_hubstudio_running():
            print("✅ Hubstudio软件已在运行")
            return self._wait_for_api_ready()
        
        # 获取安装路径
        install_path = self.config.get("hubstudio", {}).get("install_path")
        if not install_path:
            try:
                install_path = self._find_hubstudio_path()
            except FileNotFoundError as e:
                print(str(e))
                return False
        
        if not os.path.exists(install_path):
            print(f"❌ Hubstudio安装路径不存在: {install_path}")
            return False
        
        try:
            print(f"🔧 启动Hubstudio: {install_path}")
            
            # 启动Hubstudio软件
            self.hubstudio_process = subprocess.Popen(
                [install_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            
            print("⏳ 等待Hubstudio软件启动...")
            
            # 等待软件启动
            timeout = self.config.get("hubstudio", {}).get("startup_timeout", 60)
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if self._is_hubstudio_running():
                    print("✅ Hubstudio软件启动成功")
                    # 等待API服务就绪
                    return self._wait_for_api_ready()
                time.sleep(2)
            
            print("❌ Hubstudio软件启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动Hubstudio软件失败: {e}")
            return False
    
    def _wait_for_api_ready(self, timeout: int = 30) -> bool:
        """等待API服务就绪"""
        print("⏳ 等待Hubstudio API服务就绪...")
        
        api_port = self.config.get("hubstudio", {}).get("api_port", 6873)
        base_url = f"http://localhost:{api_port}"
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 尝试访问API健康检查端点
                response = requests.get(f"{base_url}/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Hubstudio API服务就绪")
                    self.api_ready = True
                    return True
            except requests.exceptions.RequestException:
                pass
            
            try:
                # 如果健康检查不可用，尝试访问登录端点
                response = requests.post(f"{base_url}/login", 
                                       json={"test": "connection"}, 
                                       timeout=5)
                # 即使返回错误，只要能连接就说明API服务已启动
                print("✅ Hubstudio API服务就绪")
                self.api_ready = True
                return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(2)
        
        print("❌ Hubstudio API服务启动超时")
        return False
    
    def login_api(self) -> bool:
        """登录Hubstudio API"""
        print("🔑 第二步：登录Hubstudio API...")
        
        if not self.api_ready:
            print("❌ API服务未就绪")
            return False
        
        api_config = self.config.get("api", {})
        app_id = api_config.get("app_id")
        app_secret = api_config.get("app_secret")
        group_code = api_config.get("group_code")
        
        if not all([app_id, app_secret, group_code]):
            print("❌ API凭证配置不完整，请检查配置文件")
            print("   需要配置: app_id, app_secret, group_code")
            return False
        
        api_port = self.config.get("hubstudio", {}).get("api_port", 6873)
        url = f"http://localhost:{api_port}/login"
        
        data = {
            "appId": app_id,
            "appSecret": app_secret,
            "groupCode": group_code
        }
        
        try:
            response = requests.post(url, 
                                   headers={"Content-Type": "application/json"},
                                   json=data, 
                                   timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                print("✅ API登录成功")
                return True
            else:
                print(f"❌ API登录失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ API登录异常: {e}")
            return False
    
    def get_browser_environments(self) -> List[Dict]:
        """获取浏览器环境列表"""
        print("📋 第三步：获取浏览器环境列表...")
        
        api_port = self.config.get("hubstudio", {}).get("api_port", 6873)
        url = f"http://localhost:{api_port}/api/v1/env/list"
        
        data = {"current": 1, "size": 200}
        
        try:
            response = requests.post(url,
                                   headers={"Content-Type": "application/json"},
                                   json=data,
                                   timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                environments = result["data"]["list"]
                print(f"✅ 获取到 {len(environments)} 个浏览器环境")
                
                # 显示环境信息
                for i, env in enumerate(environments[:5]):  # 只显示前5个
                    print(f"   {i+1}. {env.get('containerName', '未命名')} (ID: {env.get('containerCode')})")
                
                return environments
            else:
                print(f"❌ 获取环境列表失败: {result.get('msg')}")
                return []
                
        except Exception as e:
            print(f"❌ 获取环境列表异常: {e}")
            return []
    
    def open_browser_environment(self, container_code: str, startup_urls: List[str] = None) -> bool:
        """打开指定的浏览器环境"""
        print(f"🌐 第四步：打开浏览器环境 {container_code}...")
        
        api_port = self.config.get("hubstudio", {}).get("api_port", 6873)
        url = f"http://localhost:{api_port}/api/v1/browser/start"
        
        # 使用配置中的默认URL或传入的URL
        if not startup_urls:
            startup_urls = self.config.get("automation", {}).get("default_startup_urls", [])
        
        data = {
            "containerCode": container_code,
            "isHeadless": False,  # 显示浏览器窗口
            "isWebDriverReadOnlyMode": False
        }
        
        if startup_urls:
            data["containerTabs"] = startup_urls
        
        try:
            response = requests.post(url,
                                   headers={"Content-Type": "application/json"},
                                   json=data,
                                   timeout=30)
            result = response.json()
            
            if result.get("code") == 0:
                browser_info = result["data"]
                print(f"✅ 浏览器环境 {container_code} 打开成功")
                print(f"   调试端口: {browser_info.get('debuggingPort')}")
                print(f"   浏览器ID: {browser_info.get('browserID')}")
                
                # 保存环境信息
                self.opened_environments[container_code] = browser_info
                
                # 等待环境完全启动
                wait_time = self.config.get("automation", {}).get("wait_after_startup", 5)
                print(f"⏳ 等待环境完全启动 ({wait_time}秒)...")
                time.sleep(wait_time)
                
                return True
            else:
                print(f"❌ 打开浏览器环境失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            print(f"❌ 打开浏览器环境异常: {e}")
            return False
    
    def connect_to_browser(self, container_code: str) -> Optional[webdriver.Chrome]:
        """连接到浏览器环境进行自动化操作"""
        print(f"🔗 第五步：连接到浏览器环境进行自动化...")
        
        if container_code not in self.opened_environments:
            print(f"❌ 环境 {container_code} 未打开")
            return None
        
        browser_info = self.opened_environments[container_code]
        debug_port = browser_info.get("debuggingPort")
        
        if not debug_port:
            print("❌ 未获取到调试端口")
            return None
        
        try:
            # 配置Chrome选项连接到现有浏览器
            options = Options()
            options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            # 创建WebDriver连接
            driver = webdriver.Chrome(options=options)
            print(f"✅ 成功连接到浏览器环境，可以进行自动化操作")
            
            return driver
            
        except Exception as e:
            print(f"❌ 连接浏览器环境失败: {e}")
            return None
    
    def upload_file_example(self, driver: webdriver.Chrome, file_path: str, upload_url: str):
        """文件上传示例"""
        print(f"📤 第六步：执行文件上传操作...")
        
        try:
            print(f"📁 上传文件: {file_path}")
            print(f"🌐 目标网站: {upload_url}")
            
            # 访问上传网站
            driver.get(upload_url)
            
            # 等待页面加载
            wait = WebDriverWait(driver, 10)
            
            # 查找文件上传元素（这里使用通用选择器，实际使用时需要根据具体网站调整）
            upload_element = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            
            # 上传文件
            upload_element.send_keys(os.path.abspath(file_path))
            print(f"✅ 文件上传操作完成")
            
            # 等待一下让用户看到结果
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ 文件上传失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        print("🧹 清理资源...")
        
        # 关闭所有浏览器环境
        if self.opened_environments:
            api_port = self.config.get("hubstudio", {}).get("api_port", 6873)
            try:
                url = f"http://localhost:{api_port}/api/v1/browser/stop-all"
                requests.post(url, 
                            headers={"Content-Type": "application/json"},
                            json={"clearOpening": True},
                            timeout=10)
                print("✅ 已关闭所有浏览器环境")
            except:
                pass
        
        print("✅ 资源清理完成")
    
    def run_complete_automation(self, container_code: str = None, 
                              file_path: str = None, upload_url: str = None):
        """运行完整的自动化流程"""
        print("🎬 开始Hubstudio完整自动化流程")
        print("=" * 60)
        
        try:
            # 第一步：启动Hubstudio软件
            if not self.start_hubstudio_software():
                print("❌ 流程终止：Hubstudio软件启动失败")
                return False
            
            # 第二步：登录API
            if not self.login_api():
                print("❌ 流程终止：API登录失败")
                return False
            
            # 第三步：获取环境列表
            environments = self.get_browser_environments()
            if not environments:
                print("❌ 流程终止：没有可用的浏览器环境")
                return False
            
            # 第四步：选择并打开环境
            if not container_code:
                # 自动选择第一个环境
                container_code = str(environments[0]["containerCode"])
                print(f"🎯 自动选择环境: {environments[0].get('containerName')} ({container_code})")
            
            if not self.open_browser_environment(container_code):
                print("❌ 流程终止：浏览器环境打开失败")
                return False
            
            # 第五步：连接浏览器进行自动化（可选）
            if file_path and upload_url:
                driver = self.connect_to_browser(container_code)
                if driver:
                    # 第六步：执行文件上传
                    self.upload_file_example(driver, file_path, upload_url)
                    driver.quit()
            
            print("🎉 完整自动化流程执行成功！")
            print(f"✅ Hubstudio软件已启动")
            print(f"✅ 浏览器环境 {container_code} 已打开")
            print(f"✅ 您现在可以在打开的浏览器中进行手动操作")
            
            return True
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断操作")
            return False
        except Exception as e:
            print(f"\n❌ 自动化流程异常: {e}")
            return False


def main():
    """主函数"""
    print("🚀 Hubstudio 完整自动化系统")
    print("=" * 50)
    print("本程序将自动执行以下步骤:")
    print("1. 启动Hubstudio软件")
    print("2. 登录API服务")
    print("3. 获取浏览器环境列表")
    print("4. 打开指定的浏览器环境")
    print("5. 可选：进行文件上传等自动化操作")
    print("=" * 50)
    
    # 创建自动化控制器
    automation = HubstudioCompleteAutomation()
    
    try:
        # 运行完整自动化流程
        success = automation.run_complete_automation()
        
        if success:
            print("\n" + "=" * 50)
            print("🎯 自动化流程完成！")
            print("浏览器环境已打开，您可以:")
            print("1. 在打开的浏览器中手动进行操作")
            print("2. 保持环境运行状态")
            print("3. 或者按任意键关闭环境")
            print("=" * 50)
            
            input("\n按回车键关闭环境并退出程序...")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
    finally:
        automation.cleanup()


if __name__ == "__main__":
    main()
