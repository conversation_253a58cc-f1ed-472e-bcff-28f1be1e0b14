#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI对话框模块
提供各种对话框功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os

class FileUploadDialog:
    """文件上传对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("文件上传设置")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="文件上传配置", font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="选择文件", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT)
        
        # 上传设置区域
        upload_frame = ttk.LabelFrame(main_frame, text="上传设置", padding="10")
        upload_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 目标网站
        ttk.Label(upload_frame, text="目标网站URL:").pack(anchor=tk.W)
        self.url_var = tk.StringVar()
        url_entry = ttk.Entry(upload_frame, textvariable=self.url_var, width=60)
        url_entry.pack(fill=tk.X, pady=(5, 10))
        
        # 常用网站快速选择
        ttk.Label(upload_frame, text="常用网站:").pack(anchor=tk.W)
        url_frame = ttk.Frame(upload_frame)
        url_frame.pack(fill=tk.X, pady=(5, 10))
        
        common_sites = [
            ("测试网站", "https://httpbin.org/post"),
            ("文件上传测试", "https://file.io"),
            ("图片上传", "https://postimages.org")
        ]
        
        for i, (name, url) in enumerate(common_sites):
            ttk.Button(url_frame, text=name, 
                      command=lambda u=url: self.url_var.set(u)).pack(side=tk.LEFT, padx=(0, 5))
        
        # 上传选择器
        ttk.Label(upload_frame, text="文件选择器 (CSS选择器):").pack(anchor=tk.W)
        self.selector_var = tk.StringVar(value="input[type='file']")
        ttk.Entry(upload_frame, textvariable=self.selector_var, width=60).pack(fill=tk.X, pady=(5, 0))
        
        # 高级选项
        advanced_frame = ttk.LabelFrame(main_frame, text="高级选项", padding="10")
        advanced_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 等待时间
        wait_frame = ttk.Frame(advanced_frame)
        wait_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(wait_frame, text="上传后等待时间(秒):").pack(side=tk.LEFT)
        self.wait_time_var = tk.IntVar(value=3)
        ttk.Spinbox(wait_frame, from_=1, to=30, textvariable=self.wait_time_var, width=10).pack(side=tk.RIGHT)
        
        # 自动提交
        self.auto_submit_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(advanced_frame, text="上传后自动提交表单", 
                       variable=self.auto_submit_var).pack(anchor=tk.W, pady=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="确定", command=self.ok).pack(side=tk.RIGHT)
        
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择要上传的文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("文档文件", "*.pdf *.doc *.docx *.txt"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.file_path_var.set(filename)
            
    def ok(self):
        """确定按钮"""
        file_path = self.file_path_var.get().strip()
        url = self.url_var.get().strip()
        
        if not file_path:
            messagebox.showerror("错误", "请选择要上传的文件")
            return
            
        if not os.path.exists(file_path):
            messagebox.showerror("错误", "选择的文件不存在")
            return
            
        if not url:
            messagebox.showerror("错误", "请输入目标网站URL")
            return
            
        if not url.startswith(('http://', 'https://')):
            messagebox.showerror("错误", "请输入有效的URL（以http://或https://开头）")
            return
            
        self.result = {
            'file_path': file_path,
            'url': url,
            'selector': self.selector_var.get(),
            'wait_time': self.wait_time_var.get(),
            'auto_submit': self.auto_submit_var.get()
        }
        
        self.dialog.destroy()
        
    def cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()
        
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result

class EnvironmentSelectorDialog:
    """环境选择对话框"""
    
    def __init__(self, parent, environments):
        self.parent = parent
        self.environments = environments
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("选择浏览器环境")
        self.dialog.geometry("600x400")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"600x400+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="选择要打开的浏览器环境", font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 环境列表
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 创建Treeview
        columns = ('ID', '名称', '代理类型', '最后使用IP')
        self.env_tree = ttk.Treeview(list_frame, columns=columns, show='headings')
        
        # 设置列标题和宽度
        self.env_tree.heading('ID', text='环境ID')
        self.env_tree.heading('名称', text='环境名称')
        self.env_tree.heading('代理类型', text='代理类型')
        self.env_tree.heading('最后使用IP', text='最后使用IP')
        
        self.env_tree.column('ID', width=100)
        self.env_tree.column('名称', width=200)
        self.env_tree.column('代理类型', width=120)
        self.env_tree.column('最后使用IP', width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.env_tree.yview)
        self.env_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.env_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 填充数据
        for env in self.environments:
            self.env_tree.insert('', 'end', values=(
                env.get('containerCode', ''),
                env.get('containerName', ''),
                env.get('proxyTypeName', ''),
                env.get('lastUsedIp', '')
            ))
        
        # 双击事件
        self.env_tree.bind('<Double-1>', self.on_double_click)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="确定", command=self.ok).pack(side=tk.RIGHT)
        
    def on_double_click(self, event):
        """双击事件"""
        self.ok()
        
    def ok(self):
        """确定按钮"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个环境")
            return
            
        item = self.env_tree.item(selection[0])
        container_code = item['values'][0]
        
        # 找到对应的环境数据
        for env in self.environments:
            if str(env.get('containerCode')) == str(container_code):
                self.result = env
                break
                
        self.dialog.destroy()
        
    def cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()
        
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result

class SettingsDialog:
    """设置对话框"""
    
    def __init__(self, parent, config):
        self.parent = parent
        self.config = config.copy()
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("高级设置")
        self.dialog.geometry("500x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"500x500+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Hubstudio设置页
        hubstudio_frame = ttk.Frame(notebook, padding="20")
        notebook.add(hubstudio_frame, text="Hubstudio设置")
        
        # API设置页
        api_frame = ttk.Frame(notebook, padding="20")
        notebook.add(api_frame, text="API设置")
        
        # 自动化设置页
        automation_frame = ttk.Frame(notebook, padding="20")
        notebook.add(automation_frame, text="自动化设置")
        
        # 创建各页内容
        self.create_hubstudio_settings(hubstudio_frame)
        self.create_api_settings(api_frame)
        self.create_automation_settings(automation_frame)
        
        # 按钮区域
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="确定", command=self.ok).pack(side=tk.RIGHT)
        
    def create_hubstudio_settings(self, parent):
        """创建Hubstudio设置"""
        hubstudio_config = self.config.get('hubstudio', {})
        
        # 启动超时
        ttk.Label(parent, text="启动超时时间(秒):").pack(anchor=tk.W, pady=(0, 5))
        self.startup_timeout_var = tk.IntVar(value=hubstudio_config.get('startup_timeout', 60))
        ttk.Spinbox(parent, from_=30, to=300, textvariable=self.startup_timeout_var, width=20).pack(anchor=tk.W, pady=(0, 15))
        
        # API端口
        ttk.Label(parent, text="API端口:").pack(anchor=tk.W, pady=(0, 5))
        self.api_port_var = tk.IntVar(value=hubstudio_config.get('api_port', 6873))
        ttk.Spinbox(parent, from_=1000, to=65535, textvariable=self.api_port_var, width=20).pack(anchor=tk.W)
        
    def create_api_settings(self, parent):
        """创建API设置"""
        ttk.Label(parent, text="API连接超时时间(秒):").pack(anchor=tk.W, pady=(0, 5))
        self.api_timeout_var = tk.IntVar(value=30)
        ttk.Spinbox(parent, from_=10, to=120, textvariable=self.api_timeout_var, width=20).pack(anchor=tk.W, pady=(0, 15))
        
        ttk.Label(parent, text="API重试次数:").pack(anchor=tk.W, pady=(0, 5))
        self.api_retry_var = tk.IntVar(value=3)
        ttk.Spinbox(parent, from_=1, to=10, textvariable=self.api_retry_var, width=20).pack(anchor=tk.W)
        
    def create_automation_settings(self, parent):
        """创建自动化设置"""
        automation_config = self.config.get('automation', {})
        
        # 启动后等待时间
        ttk.Label(parent, text="环境启动后等待时间(秒):").pack(anchor=tk.W, pady=(0, 5))
        self.wait_after_startup_var = tk.IntVar(value=automation_config.get('wait_after_startup', 5))
        ttk.Spinbox(parent, from_=1, to=30, textvariable=self.wait_after_startup_var, width=20).pack(anchor=tk.W, pady=(0, 15))
        
        # 默认启动URL
        ttk.Label(parent, text="默认启动URL:").pack(anchor=tk.W, pady=(0, 5))
        default_urls = automation_config.get('default_startup_urls', ['https://www.baidu.com'])
        self.startup_urls_var = tk.StringVar(value='\n'.join(default_urls))
        url_text = tk.Text(parent, height=5, width=50)
        url_text.pack(anchor=tk.W, pady=(0, 15))
        url_text.insert('1.0', self.startup_urls_var.get())
        self.url_text = url_text
        
    def ok(self):
        """确定按钮"""
        try:
            # 更新配置
            self.config['hubstudio']['startup_timeout'] = self.startup_timeout_var.get()
            self.config['hubstudio']['api_port'] = self.api_port_var.get()
            self.config['automation']['wait_after_startup'] = self.wait_after_startup_var.get()
            
            # 处理URL列表
            urls_text = self.url_text.get('1.0', tk.END).strip()
            urls = [url.strip() for url in urls_text.split('\n') if url.strip()]
            self.config['automation']['default_startup_urls'] = urls
            
            self.result = self.config
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
            
    def cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()
        
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result
