#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hubstudio自动化系统主启动文件
用户可以选择GUI界面或命令行模式
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查依赖库"""
    required_modules = [
        'tkinter', 'requests', 'selenium', 'psutil'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'tkinter':
                import tkinter
            elif module == 'requests':
                import requests
            elif module == 'selenium':
                import selenium
            elif module == 'psutil':
                import psutil
        except ImportError:
            missing_modules.append(module)
    
    return missing_modules

def install_dependencies(missing_modules):
    """安装缺失的依赖"""
    if not missing_modules:
        return True
    
    print("检测到缺失的依赖库:")
    for module in missing_modules:
        print(f"  - {module}")
    
    choice = input("\n是否自动安装缺失的依赖? (y/n): ").lower().strip()
    
    if choice == 'y':
        import subprocess
        try:
            for module in missing_modules:
                if module == 'tkinter':
                    print("tkinter是Python标准库，请检查Python安装")
                    continue
                
                print(f"正在安装 {module}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', module])
            
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            return False
    else:
        print("请手动安装缺失的依赖后重新运行程序")
        return False

def show_mode_selection():
    """显示模式选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 设置对话框
    choice = messagebox.askyesnocancel(
        "Hubstudio自动化系统",
        "请选择运行模式:\n\n"
        "是(Yes) - 图形界面模式 (推荐)\n"
        "否(No) - 命令行模式\n"
        "取消(Cancel) - 退出程序",
        icon='question'
    )
    
    root.destroy()
    return choice

def run_gui_mode():
    """运行GUI模式"""
    try:
        print("🚀 启动图形界面模式...")

        # 检查GUI相关文件
        gui_files = ['hubstudio_gui.py', 'gui_dialogs.py']
        missing_files = [f for f in gui_files if not os.path.exists(f)]

        if missing_files:
            print(f"❌ 缺少GUI文件: {', '.join(missing_files)}")
            return False

        from hubstudio_gui import HubstudioGUI

        app = HubstudioGUI()
        app.run()

    except ImportError as e:
        print(f"❌ GUI模块导入失败: {e}")
        print("请确保所有GUI相关文件存在且无语法错误")
        return False
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

def run_cli_mode():
    """运行命令行模式"""
    try:
        print("🚀 启动命令行模式...")
        from simple_usage_example import main as cli_main
        
        cli_main()
        
    except ImportError as e:
        print(f"❌ 命令行模块导入失败: {e}")
        print("请确保 simple_usage_example.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 命令行模式启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 Hubstudio 自动化控制系统")
    print("=" * 60)
    print("版本: 1.0.0")
    print("功能: 自动启动Hubstudio → 打开浏览器环境 → 文件上传")
    print("=" * 60)
    
    # 检查依赖
    print("🔍 检查系统依赖...")
    missing_modules = check_dependencies()
    
    if missing_modules:
        if not install_dependencies(missing_modules):
            print("❌ 依赖检查失败，程序退出")
            input("按回车键退出...")
            return
    else:
        print("✅ 依赖检查通过")
    
    # 检查核心文件
    core_files = [
        'hubstudio_complete_automation.py',
        'hubstudio_config.json'
    ]
    
    missing_files = []
    for file in core_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少核心文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("请确保所有文件完整后重新运行")
        input("按回车键退出...")
        return
    
    print("✅ 文件检查通过")
    print()
    
    # 选择运行模式
    try:
        choice = show_mode_selection()
        
        if choice is True:  # GUI模式
            success = run_gui_mode()
        elif choice is False:  # 命令行模式
            success = run_cli_mode()
        else:  # 取消
            print("👋 程序已取消")
            return
        
        if not success:
            print("❌ 程序运行失败")
            input("按回车键退出...")
    
    except Exception as e:
        print(f"❌ 程序运行异常: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        input("按回车键退出...")
