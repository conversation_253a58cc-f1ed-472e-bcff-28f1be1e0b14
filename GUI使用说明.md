# Hubstudio GUI 自动化控制系统使用说明

## 🎯 系统概述

这是一个带有图形化界面的Hubstudio自动化控制系统，提供直观易用的操作界面，实现：
- 自动启动Hubstudio软件
- 通过API管理浏览器环境
- 文件上传自动化
- 实时日志监控

## 🚀 快速开始

### 1. 启动程序

#### 方式一：双击批处理文件
```
双击 "启动GUI.bat" 文件
```

#### 方式二：命令行启动
```bash
python hubstudio_gui.py
```

### 2. 配置API信息

在界面顶部的"配置设置"区域填入：
- **APP ID**: 您的Hubstudio API应用ID
- **APP Secret**: 您的API密钥
- **Group Code**: 您的团队代码

点击"保存配置"按钮保存设置。

### 3. 启动自动化流程

点击"🚀 启动完整流程"按钮，系统将自动：
1. 启动Hubstudio软件
2. 登录API服务
3. 获取浏览器环境列表
4. 打开第一个可用环境

## 📋 界面功能说明

### 配置设置区域
- **Hubstudio路径**: 软件安装路径（可自动检测）
- **API配置**: 填入您的API凭证信息
- **保存配置**: 保存当前配置到文件

### 控制操作区域
- **🚀 启动完整流程**: 一键启动完整自动化流程
- **🛑 停止**: 停止当前运行的流程
- **🔄 刷新环境**: 刷新浏览器环境列表
- **🧹 清理资源**: 清理所有打开的资源
- **进度条**: 显示当前操作进度

### 浏览器环境区域
- **环境列表**: 显示所有可用的浏览器环境
- **打开选中环境**: 打开选中的浏览器环境
- **关闭选中环境**: 关闭选中的浏览器环境
- **文件上传**: 在选中环境中进行文件上传
- **高级设置**: 打开高级配置对话框

### 操作日志区域
- **实时日志**: 显示所有操作的详细日志
- **清空日志**: 清空当前日志内容
- **保存日志**: 将日志保存到文件

## 🔧 高级功能

### 文件上传功能

1. 选择一个已打开的浏览器环境
2. 点击"文件上传"按钮
3. 在弹出的对话框中：
   - 选择要上传的文件
   - 输入目标网站URL
   - 配置上传选择器（CSS选择器）
   - 设置高级选项
4. 点击"确定"开始上传

### 高级设置

点击"高级设置"按钮可以配置：

#### Hubstudio设置
- **启动超时时间**: 等待软件启动的最大时间
- **API端口**: API服务端口号

#### API设置
- **连接超时时间**: API连接超时设置
- **重试次数**: 连接失败时的重试次数

#### 自动化设置
- **启动后等待时间**: 环境启动后的等待时间
- **默认启动URL**: 环境打开时自动访问的网址

## 💡 使用技巧

### 1. 批量操作
- 可以通过环境列表选择多个环境进行批量操作
- 使用Ctrl+点击选择多个环境

### 2. 快速配置
- 文件上传对话框提供常用网站快速选择
- 可以保存常用的上传配置

### 3. 日志管理
- 实时查看操作日志，了解程序运行状态
- 可以保存日志文件用于问题排查

### 4. 错误处理
- 程序会自动处理常见错误并显示提示
- 查看日志了解详细错误信息

## 🛠️ 故障排除

### 问题1: 程序启动失败
**解决方案:**
1. 确认Python环境已正确安装
2. 检查是否安装了必要的依赖库
3. 以管理员权限运行程序

### 问题2: Hubstudio启动失败
**解决方案:**
1. 检查Hubstudio安装路径是否正确
2. 确认软件未被其他程序占用
3. 检查系统资源是否充足

### 问题3: API连接失败
**解决方案:**
1. 验证API凭证是否正确
2. 检查网络连接
3. 确认Hubstudio软件已完全启动

### 问题4: 环境打开失败
**解决方案:**
1. 检查环境是否被其他程序占用
2. 确认系统资源充足
3. 尝试重启Hubstudio软件

### 问题5: 文件上传失败
**解决方案:**
1. 检查文件路径是否正确
2. 验证目标网站URL是否有效
3. 确认CSS选择器是否正确
4. 检查网络连接状态

## 📁 文件结构

```
📁 项目目录
├── 🐍 hubstudio_gui.py              # 主GUI程序
├── 🐍 hubstudio_complete_automation.py  # 自动化核心
├── 🐍 gui_dialogs.py               # GUI对话框模块
├── ⚙️ hubstudio_config.json        # 配置文件
├── 🔧 启动GUI.bat                  # 启动脚本
└── 📖 GUI使用说明.md               # 本说明文档
```

## 🎯 系统特点

### ✅ 用户友好
- 直观的图形化界面
- 清晰的操作流程
- 实时状态反馈

### ✅ 功能完整
- 完整的自动化流程
- 灵活的配置选项
- 强大的文件上传功能

### ✅ 稳定可靠
- 完善的错误处理
- 详细的日志记录
- 自动资源清理

### ✅ 易于扩展
- 模块化设计
- 可自定义配置
- 支持功能扩展

## 📞 技术支持

如果在使用过程中遇到问题：
1. 查看操作日志了解详细信息
2. 参考本说明文档的故障排除部分
3. 联系Hubstudio官方技术支持

---

**享受自动化带来的便利！** 🎉
